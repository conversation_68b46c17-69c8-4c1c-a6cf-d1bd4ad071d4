#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试控件识别问题的测试脚本
分析坐标 (761, 446) 处为什么识别到 page list 而不是"添加"按钮
"""

import sys
import os
import time
import traceback

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def analyze_widget_at_coordinate(x, y):
    """分析指定坐标处的控件识别过程"""
    print(f"\n{'='*80}")
    print(f"开始分析坐标 ({x}, {y}) 处的控件识别")
    print(f"{'='*80}")

    # 创建UNI实例
    uni = UNI()

    # 设置DISPLAY环境变量
    os.environ['DISPLAY'] = ':0'

    try:
        # 1. 首先获取活动窗口信息
        print(f"\n[步骤1] 获取活动窗口信息...")
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = uni._get_active_window2(x, y)

        if active_window:
            print(f"  活动窗口: {active_window.name}")
            print(f"  进程ID: {processid}")
            print(f"  窗口区域: {activewindow_region}")
            print(f"  窗口角色: {windowRoleName}")
            print(f"  子控件数量: {windowChildCount}")
        else:
            print("  未找到活动窗口")
            return

        # 2. 尝试X11层级检测
        print(f"\n[步骤2] 尝试X11层级检测...")
        topmost_element = uni._find_topmost_element_at_point(x, y)

        if topmost_element == "SKIP_UNKNOWN_WINDOW":
            print("  X11检测结果: 跳过Unknown窗口")
        elif topmost_element:
            print(f"  X11检测成功: {topmost_element.name if topmost_element.name else 'unnamed'}")
            print(f"  控件角色: {topmost_element.getRoleName()}")

            # 提取控件信息
            data = uni._extract_element_info(topmost_element)
            print(f"  控件详细信息: {data}")
        else:
            print("  X11检测失败，将使用传统方法")

        # 3. 使用完整的控件识别流程
        print(f"\n[步骤3] 使用完整的控件识别流程...")
        uni.childEle = None
        uni._find_accessible_at_point(active_window, x, y, activewindow_region)

        if uni.childEle:
            print(f"  找到控件: {uni.childEle.name if uni.childEle.name else 'unnamed'}")
            print(f"  控件角色: {uni.childEle.getRoleName()}")

            # 提取完整的控件信息
            data = uni._extract_element_info(uni.childEle)
            data["RecordPosition"] = (x, y)
            data["WindowRoleName"] = windowRoleName
            data["WindowChildCount"] = windowChildCount

            print(f"\n[最终结果] 控件信息:")
            for key, value in data.items():
                print(f"  {key}: {value}")
        else:
            print("  未找到控件")

        # 4. 分析周围区域的控件
        print(f"\n[步骤4] 分析周围区域的控件...")
        offsets = [(-10, 0), (10, 0), (0, -10), (0, 10), (-5, -5), (5, 5)]

        for dx, dy in offsets:
            test_x, test_y = x + dx, y + dy
            print(f"\n  测试坐标 ({test_x}, {test_y}):")

            uni.childEle = None
            uni._find_accessible_at_point(active_window, test_x, test_y, activewindow_region)

            if uni.childEle:
                print(f"    找到控件: {uni.childEle.name if uni.childEle.name else 'unnamed'}")
                print(f"    控件角色: {uni.childEle.getRoleName()}")

                # 获取控件坐标
                try:
                    import pyatspi
                    component = uni.childEle.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                        print(f"    控件位置: x={extents.x}, y={extents.y}, w={extents.width}, h={extents.height}")
                except:
                    pass
            else:
                print(f"    未找到控件")

        # 5. 使用kdk_getElement_Uni方法进行对比
        print(f"\n[步骤5] 使用kdk_getElement_Uni方法进行对比...")
        try:
            widget_info, info_text = uni.kdk_getElement_Uni(x, y, False)
            print(f"  kdk_getElement_Uni结果: {info_text}")
            if widget_info and not widget_info.get('error'):
                print(f"  控件名称: {widget_info.get('Name', 'Unknown')}")
                print(f"  控件角色: {widget_info.get('Rolename', 'Unknown')}")
                print(f"  控件坐标: {widget_info.get('Coords', {})}")
        except Exception as e:
            print(f"  kdk_getElement_Uni调用失败: {e}")

    except Exception as e:
        print(f"[ERROR] 分析过程中发生错误: {e}")
        traceback.print_exc()

def analyze_widget_hierarchy(x, y):
    """分析指定坐标处的控件层次结构"""
    print(f"\n{'='*80}")
    print(f"分析坐标 ({x}, {y}) 处的控件层次结构")
    print(f"{'='*80}")

    try:
        import pyatspi

        # 创建UNI实例
        uni = UNI()
        os.environ['DISPLAY'] = ':0'

        # 获取活动窗口
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = uni._get_active_window2(x, y)

        if not active_window:
            print("未找到活动窗口")
            return

        print(f"活动窗口: {active_window.name}")
        print(f"窗口区域: {activewindow_region}")

        # 递归遍历窗口中的所有控件
        def traverse_widgets(widget, depth=0, max_depth=5):
            if depth > max_depth:
                return

            try:
                # 获取控件信息
                role = widget.getRoleName()
                name = widget.name if widget.name else 'unnamed'

                # 获取控件位置
                component = widget.queryComponent()
                if component:
                    extents = component.getExtents(pyatspi.DESKTOP_COORDS)

                    # 检查坐标是否在控件内
                    contains_point = (extents.x <= x < extents.x + extents.width and
                                    extents.y <= y < extents.y + extents.height)

                    indent = "  " * depth
                    marker = "🎯" if contains_point else "  "

                    print(f"{indent}{marker} {name} ({role}) - "
                          f"位置:({extents.x},{extents.y}) 大小:({extents.width}x{extents.height})")

                    # 如果包含目标坐标，继续深入查找
                    if contains_point:
                        for i in range(widget.childCount):
                            try:
                                child = widget.getChildAtIndex(i)
                                if child:
                                    traverse_widgets(child, depth + 1, max_depth)
                            except:
                                continue

            except Exception as e:
                print(f"{'  ' * depth}❌ 遍历控件时出错: {e}")

        print(f"\n控件层次结构 (🎯 表示包含坐标 ({x}, {y}) 的控件):")
        traverse_widgets(active_window)

    except Exception as e:
        print(f"[ERROR] 分析控件层次结构时发生错误: {e}")
        traceback.print_exc()

def main():
    """主函数"""
    # 分析问题坐标的控件层次结构
    analyze_widget_hierarchy(761, 446)

    # 分析问题坐标
    analyze_widget_at_coordinate(761, 446)

if __name__ == "__main__":
    main()
