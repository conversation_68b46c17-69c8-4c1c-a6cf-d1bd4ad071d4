#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试 auto_recording_manager.py 的完整控件识别流程
"""

import sys
import os
import time
import threading

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

class DebugWidgetAnalyzer:
    """调试版本的 WidgetAnalyzer，完全模拟 auto_recording_manager.py 的逻辑"""
    
    def __init__(self, debug: bool = True):
        self.debug = debug
        self.uni = None
        
        try:
            self.uni = UNI()
            if self.debug:
                print("[DEBUG] UNI控件分析器初始化成功", file=sys.stderr)
        except Exception as e:
            print(f"[ERROR] UNI控件分析器初始化失败: {e}", file=sys.stderr)
    
    def _should_skip_widget_analysis(self, x: int, y: int) -> bool:
        """检查是否应该跳过控件分析（模拟原方法）"""
        return False  # 简化版，不跳过
    
    def analyze_widget_at(self, x: int, y: int, interrupt_flag: threading.Event = None):
        """完全模拟 auto_recording_manager.py 的 analyze_widget_at 方法"""
        if not self.uni:
            return None, "UNI模块不可用"

        try:
            if self.debug:
                print(f"[DEBUG] 🔍 开始控件识别: 坐标({x}, {y})", file=sys.stderr)

            # 🚀 性能优化：快速检查是否为可能导致卡顿的区域
            if self._should_skip_widget_analysis(x, y):
                return None, "跳过可能导致卡顿的控件识别"

            # 添加超时机制，防止长时间阻塞
            start_time = time.time()
            timeout = 3.0  # 3秒超时

            # 🚀 优化：智能缓存清理，只在必要时清除
            # 防御性检查：确保desktop_cache是字典类型
            if hasattr(self.uni, 'desktop_cache') and self.uni.desktop_cache is not None and isinstance(self.uni.desktop_cache, dict) and 'desktop_object' in self.uni.desktop_cache:
                # 🚀 优化：增加缓存超时时间到300ms，减少频繁刷新
                cache_entry = self.uni.desktop_cache['desktop_object']
                if time.time() - cache_entry['time'] > 0.3:  # 300ms超时
                    del self.uni.desktop_cache['desktop_object']
                    if self.debug:
                        print(f"[DEBUG] 已清除过期的桌面缓存", file=sys.stderr)

            # 如果UNI模块有_get_fresh_desktop方法，先触发桌面刷新
            if hasattr(self.uni, '_get_fresh_desktop'):
                # 检查是否超时
                if time.time() - start_time > timeout:
                    print(f"[WARNING] 控件识别超时，跳过桌面刷新", file=sys.stderr)
                    return None, "控件识别超时"

                # 检查是否被中断
                if interrupt_flag and interrupt_flag.is_set():
                    print(f"[INFO] 控件识别被中断（桌面刷新前）", file=sys.stderr)
                    return None, "控件识别被中断"

                # 强制刷新桌面以检测新打开的应用
                # 每次控件识别前都强制刷新，确保能检测到新应用
                self.uni._last_desktop_refresh = 0
                desktop = self.uni._get_fresh_desktop()
                if self.debug:
                    app_count = desktop.childCount if desktop else 0
                    print(f"[DEBUG] 已触发桌面刷新，应用数: {app_count}", file=sys.stderr)

            # 再次检查超时
            if time.time() - start_time > timeout:
                print(f"[WARNING] 控件识别超时，跳过UNI识别", file=sys.stderr)
                return None, "控件识别超时"

            # 检查是否被中断
            if interrupt_flag and interrupt_flag.is_set():
                print(f"[INFO] 控件识别被中断（UNI识别前）", file=sys.stderr)
                return None, "控件识别被中断"

            # 🔍 关键调试：显示即将调用的方法
            print(f"[DEBUG] 🎯 即将调用: self.uni.kdk_getElement_Uni({x}, {y}, False, True)", file=sys.stderr)
            
            # 直接使用优化后的UNI模块，启用菜单控件识别
            result, info_text = self.uni.kdk_getElement_Uni(x, y, False, True)
            
            print(f"[DEBUG] 🎯 kdk_getElement_Uni 返回结果: {info_text}", file=sys.stderr)
            if result:
                print(f"[DEBUG] 🎯 识别到的控件: {result.get('Name', 'Unknown')} ({result.get('Rolename', 'Unknown')})", file=sys.stderr)

            # 检查是否被中断
            if interrupt_flag and interrupt_flag.is_set():
                print(f"[INFO] 控件识别被中断（UNI识别后）", file=sys.stderr)
                return None, "控件识别被中断"

            # 检查识别结果
            if result and not result.get('error'):
                if self.debug:
                    elapsed_time = time.time() - start_time
                    process_name = result.get('ProcessName', 'Unknown')
                    control_name = result.get('Name', 'N/A')
                    control_role = result.get('Rolename', 'N/A')
                    print(f"[DEBUG] ✅ 控件识别成功: {control_name} ({control_role}) "
                          f"进程: {process_name} 耗时: {elapsed_time:.3f}s", file=sys.stderr)
                return result, info_text
            else:
                if self.debug:
                    elapsed_time = time.time() - start_time
                    print(f"[DEBUG] ❌ 控件识别失败: {info_text} 耗时: {elapsed_time:.3f}s", file=sys.stderr)
                return None, info_text

        except Exception as e:
            error_msg = f"控件识别异常: {e}"
            print(f"[ERROR] {error_msg}", file=sys.stderr)
            if self.debug:
                import traceback
                traceback.print_exc(file=sys.stderr)
            return None, error_msg

def test_auto_recording_flow(x, y):
    """测试 auto_recording_manager.py 的完整流程"""
    print(f"\n{'='*80}")
    print(f"测试 auto_recording_manager.py 完整流程 - 坐标 ({x}, {y})")
    print(f"{'='*80}")
    
    # 设置环境
    os.environ['DISPLAY'] = ':0'
    
    # 创建调试版本的 WidgetAnalyzer
    analyzer = DebugWidgetAnalyzer(debug=True)
    
    # 测试 analyze_widget_at 方法
    print(f"\n1. 测试 analyze_widget_at 方法...")
    result, info_text = analyzer.analyze_widget_at(x, y)
    
    print(f"\n最终结果:")
    print(f"  识别结果: {info_text}")
    
    if result and not result.get('error'):
        print(f"  ✅ 成功识别控件:")
        print(f"    控件名称: {result.get('Name', 'Unknown')}")
        print(f"    控件角色: {result.get('Rolename', 'Unknown')}")
        print(f"    控件坐标: {result.get('Coords', {})}")
        
        # 分析结果
        role = result.get('Rolename', '').lower()
        name = result.get('Name', '')
        
        if 'button' in role and '添加' in name:
            print(f"    🎉 完美！正确识别为'添加'按钮")
            return True
        elif 'button' in role:
            print(f"    ⚠️  是按钮，但不是'添加'按钮: {name}")
        elif role == 'page tab list':
            print(f"    ❌ 仍然识别为 page tab list！")
            print(f"    🔍 这说明修复在 auto_recording_manager.py 的调用路径中没有生效")
        else:
            print(f"    ℹ️  识别为其他控件: {name} ({role})")
    else:
        print(f"  ❌ 识别失败")
    
    return False

def compare_direct_vs_auto_recording(x, y):
    """对比直接调用 UNI 和通过 auto_recording_manager.py 调用的结果"""
    print(f"\n{'='*80}")
    print(f"对比直接调用 vs auto_recording_manager.py 调用")
    print(f"{'='*80}")
    
    os.environ['DISPLAY'] = ':0'
    
    # 1. 直接调用 UNI
    print(f"\n1. 直接调用 UNI.kdk_getElement_Uni...")
    uni = UNI()
    direct_result, direct_info = uni.kdk_getElement_Uni(x, y, False, True)
    
    print(f"  直接调用结果: {direct_info}")
    if direct_result:
        print(f"  直接调用控件: {direct_result.get('Name', 'Unknown')} ({direct_result.get('Rolename', 'Unknown')})")
    
    # 2. 通过 auto_recording_manager.py 调用
    print(f"\n2. 通过 auto_recording_manager.py 调用...")
    analyzer = DebugWidgetAnalyzer(debug=True)
    auto_result, auto_info = analyzer.analyze_widget_at(x, y)
    
    print(f"  auto_recording 结果: {auto_info}")
    if auto_result:
        print(f"  auto_recording 控件: {auto_result.get('Name', 'Unknown')} ({auto_result.get('Rolename', 'Unknown')})")
    
    # 3. 对比结果
    print(f"\n3. 结果对比:")
    if direct_result and auto_result:
        direct_name = direct_result.get('Name', 'Unknown')
        direct_role = direct_result.get('Rolename', 'Unknown')
        auto_name = auto_result.get('Name', 'Unknown')
        auto_role = auto_result.get('Rolename', 'Unknown')
        
        if direct_name == auto_name and direct_role == auto_role:
            print(f"  ✅ 结果一致: {direct_name} ({direct_role})")
        else:
            print(f"  ❌ 结果不一致!")
            print(f"    直接调用: {direct_name} ({direct_role})")
            print(f"    auto_recording: {auto_name} ({auto_role})")
    else:
        print(f"  ❌ 其中一个调用失败")

def main():
    """主函数"""
    # 测试您的坐标
    test_auto_recording_flow(458, 330)
    
    # 对比两种调用方式
    compare_direct_vs_auto_recording(458, 330)

if __name__ == "__main__":
    main()
