#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试修复效果
"""

import sys
import os

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def simple_test():
    """简单测试"""
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        # 测试坐标
        x, y = 458, 330
        
        print(f"测试坐标: ({x}, {y})")
        print("="*50)
        
        # 1. 测试 _is_interactive_control 修复
        print(f"1. _is_interactive_control 测试:")
        page_tab_list_interactive = uni._is_interactive_control('page tab list')
        button_interactive = uni._is_interactive_control('push button')
        
        print(f"   'page tab list' 是交互控件: {page_tab_list_interactive}")
        print(f"   'push button' 是交互控件: {button_interactive}")
        
        if not page_tab_list_interactive and button_interactive:
            print(f"   ✅ _is_interactive_control 修复成功")
        else:
            print(f"   ❌ _is_interactive_control 修复失败")
        
        # 2. 测试主要的控件识别方法
        print(f"\n2. kdk_getElement_Uni 测试:")
        result, info = uni.kdk_getElement_Uni(x, y, False, True)
        
        print(f"   结果: {info}")
        if result and not result.get('error'):
            name = result.get('Name', 'Unknown')
            role = result.get('Rolename', 'Unknown')
            print(f"   控件: {name} ({role})")
            
            if 'button' in role.lower() and '添加' in name:
                print(f"   ✅ kdk_getElement_Uni 正确识别为'添加'按钮")
                return True
            elif role.lower() == 'page tab list':
                print(f"   ❌ kdk_getElement_Uni 仍然识别为 page tab list")
            else:
                print(f"   ⚠️  kdk_getElement_Uni 识别为其他: {name} ({role})")
        else:
            print(f"   ❌ kdk_getElement_Uni 识别失败")
        
        return False
        
    except Exception as e:
        print(f"[ERROR] 测试时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = simple_test()
    
    if success:
        print(f"\n🎉 修复成功！")
        print(f"现在 auto_recording_manager.py 应该能正确识别按钮控件了")
    else:
        print(f"\n❌ 修复可能不完整")

if __name__ == "__main__":
    main()
