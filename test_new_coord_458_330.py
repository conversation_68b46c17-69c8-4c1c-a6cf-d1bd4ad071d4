#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新坐标 (458, 330) 的控件识别
"""

import sys
import os

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def test_coordinate_458_330():
    """测试坐标 (458, 330)"""
    x, y = 458, 330
    
    print(f"\n{'='*80}")
    print(f"测试坐标 ({x}, {y}) 的控件识别")
    print(f"{'='*80}")
    
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        # 1. 检查活动窗口
        print("1. 检查活动窗口...")
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = uni._get_active_window2(x, y)
        
        if active_window:
            print(f"   活动窗口: {active_window.name}")
            print(f"   窗口区域: {activewindow_region}")
            print(f"   窗口角色: {windowRoleName}")
        else:
            print("   ❌ 未找到活动窗口")
            return
        
        # 2. 执行控件识别
        print(f"\n2. 执行控件识别...")
        result, info_text = uni.kdk_getElement_Uni(x, y, False, True)
        
        print(f"识别结果: {info_text}")
        
        if result and not result.get('error'):
            name = result.get('Name', 'Unknown')
            role = result.get('Rolename', 'Unknown')
            coords = result.get('Coords', {})
            
            print(f"✅ 识别到控件:")
            print(f"  控件名称: {name}")
            print(f"  控件角色: {role}")
            print(f"  控件坐标: {coords}")
            
            # 分析结果
            if 'button' in role.lower():
                print(f"  🎯 这是一个按钮控件！")
                if '添加' in name:
                    print(f"  🎉 完美！这是'添加'按钮")
                    return True
                else:
                    print(f"  ⚠️  是按钮，但不是'添加'按钮")
            elif role.lower() == 'page tab list':
                print(f"  ❌ 仍然识别为 page tab list")
                print(f"  🔍 修复可能没有生效，或者坐标不在按钮范围内")
            else:
                print(f"  ℹ️  识别为其他控件类型: {role}")
        else:
            print(f"❌ 识别失败: {info_text}")
        
        # 3. 测试智能深度搜索
        print(f"\n3. 测试智能深度搜索...")
        if active_window:
            result_element = uni._smart_deep_search_at_point(active_window, x, y, activewindow_region)
            
            if result_element:
                element_name = result_element.name if result_element.name else 'unnamed'
                element_role = result_element.getRoleName()
                
                print(f"智能深度搜索结果:")
                print(f"  控件名称: {element_name}")
                print(f"  控件角色: {element_role}")
                
                if 'button' in element_role.lower():
                    print(f"  ✅ 智能深度搜索找到按钮")
                elif element_role.lower() == 'page tab list':
                    print(f"  ❌ 智能深度搜索也返回 page tab list")
            else:
                print(f"智能深度搜索未找到控件")
        
        # 4. 查找附近的所有按钮
        print(f"\n4. 查找附近的所有按钮控件...")
        try:
            import pyatspi
            
            desktop = uni._get_fresh_desktop()
            
            # 查找打印机应用程序
            printer_app = None
            for i in range(desktop.childCount):
                try:
                    app = desktop.getChildAtIndex(i)
                    if app and app.name == 'system-config-printer':
                        printer_app = app
                        break
                except:
                    continue
            
            if printer_app:
                def find_buttons_with_distance(widget, target_x, target_y, depth=0, max_depth=10):
                    if depth > max_depth:
                        return []
                    
                    buttons = []
                    try:
                        role = widget.getRoleName().lower()
                        name = widget.name if widget.name else 'unnamed'
                        
                        if 'button' in role:
                            try:
                                component = widget.queryComponent()
                                if component:
                                    extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                    
                                    # 计算距离
                                    center_x = extents.x + extents.width // 2
                                    center_y = extents.y + extents.height // 2
                                    distance = ((center_x - target_x) ** 2 + (center_y - target_y) ** 2) ** 0.5
                                    
                                    # 检查是否包含目标坐标
                                    contains_point = (extents.x <= target_x <= extents.x + extents.width and
                                                    extents.y <= target_y <= extents.y + extents.height)
                                    
                                    buttons.append({
                                        'name': name,
                                        'role': role,
                                        'position': (extents.x, extents.y),
                                        'size': (extents.width, extents.height),
                                        'center': (center_x, center_y),
                                        'distance': distance,
                                        'contains_point': contains_point,
                                        'depth': depth
                                    })
                            except:
                                pass
                        
                        # 递归搜索子控件
                        for i in range(widget.childCount):
                            try:
                                child = widget.getChildAtIndex(i)
                                if child:
                                    child_buttons = find_buttons_with_distance(child, target_x, target_y, depth + 1, max_depth)
                                    buttons.extend(child_buttons)
                            except:
                                continue
                    except:
                        pass
                    
                    return buttons
                
                buttons = find_buttons_with_distance(printer_app, x, y)
                
                if buttons:
                    # 按距离排序
                    buttons.sort(key=lambda b: b['distance'])
                    
                    print(f"   找到 {len(buttons)} 个按钮控件（按距离排序）:")
                    for i, button in enumerate(buttons[:5]):  # 只显示前5个最近的
                        marker = "🎯" if button['contains_point'] else f"{i+1}."
                        print(f"   {marker} '{button['name']}' ({button['role']})")
                        print(f"      位置: {button['position']} 大小: {button['size']}")
                        print(f"      中心: {button['center']} 距离: {button['distance']:.1f}px")
                        print(f"      包含坐标({x}, {y}): {button['contains_point']}")
                        
                        if '添加' in button['name']:
                            print(f"      ⭐ 这是'添加'按钮！")
                        print()
                else:
                    print(f"   ❌ 未找到任何按钮控件")
            else:
                print(f"   ❌ 未找到打印机应用程序")
        
        except Exception as e:
            print(f"   ❌ 查找按钮时出错: {e}")
        
        return False
        
    except Exception as e:
        print(f"[ERROR] 测试时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_coordinate_458_330()
    
    if success:
        print(f"\n🎉 测试成功！坐标 (458, 330) 正确识别为'添加'按钮")
    else:
        print(f"\n❌ 测试失败！坐标 (458, 330) 没有正确识别为'添加'按钮")
        print(f"请检查：")
        print(f"1. '添加'按钮是否在屏幕上可见")
        print(f"2. 坐标 (458, 330) 是否确实在按钮范围内")
        print(f"3. 窗口位置是否发生了变化")

if __name__ == "__main__":
    main()
