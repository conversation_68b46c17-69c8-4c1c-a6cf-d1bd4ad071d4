#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试 page tab list 深层搜索修复
"""

import sys
import os

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def simple_test():
    """简单测试"""
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        print(f"简单测试 page tab list 深层搜索修复")
        print("="*50)
        
        # 测试之前有问题的坐标
        x, y = 397, 356
        
        print(f"测试坐标: ({x}, {y})")
        
        # 测试主要的 kdk_getElement_Uni 方法
        result, info = uni.kdk_getElement_Uni(x, y, False, True)
        
        print(f"结果: {info}")
        if result and not result.get('error'):
            name = result.get('Name', 'Unknown')
            role = result.get('Rolename', 'Unknown')
            print(f"控件: {name} ({role})")
            
            if 'button' in role.lower() and '添加' in name:
                print(f"✅ 成功！正确识别为'添加'按钮")
                return True
            elif role.lower() == 'page tab list':
                print(f"❌ 仍然识别为 page tab list")
                return False
            else:
                print(f"⚠️  识别为其他: {name} ({role})")
                return False
        else:
            print(f"❌ 识别失败")
            return False
            
    except Exception as e:
        print(f"[ERROR] 测试时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = simple_test()
    
    if success:
        print(f"\n🎉 page tab list 深层搜索修复成功！")
    else:
        print(f"\n❌ 修复可能不完整")

if __name__ == "__main__":
    main()
