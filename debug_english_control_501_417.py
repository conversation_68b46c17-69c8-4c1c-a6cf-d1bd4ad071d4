#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试坐标 (501, 417) 处的"英语"控件识别问题
"""

import sys
import os

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def debug_english_control():
    """调试英语控件识别问题"""
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        # 英语控件坐标
        x, y = 501, 417
        
        print(f"调试坐标 (501, 417) 处的'英语'控件识别问题")
        print("="*60)
        
        # 1. 测试主要的 kdk_getElement_Uni 方法
        print(f"1. 测试 kdk_getElement_Uni 方法:")
        result, info = uni.kdk_getElement_Uni(x, y, False, True)
        
        print(f"   结果: {info}")
        if result and not result.get('error'):
            name = result.get('Name', 'Unknown')
            role = result.get('Rolename', 'Unknown')
            coords = result.get('Coords', {})
            print(f"   控件: {name} ({role})")
            print(f"   坐标: {coords}")
            
            if role.lower() == 'page tab list':
                print(f"   ❌ 被识别为 page tab list，没有找到深层的'英语'控件")
                main_method_issue = True
            elif '英语' in name or 'english' in name.lower():
                print(f"   ✅ 正确识别为'英语'控件")
                main_method_issue = False
            else:
                print(f"   ⚠️  识别为其他控件")
                main_method_issue = True
        else:
            print(f"   ❌ 识别失败")
            main_method_issue = True
        
        # 2. 测试 _find_element_at_coordinates 方法（auto_recording_manager.py 使用的方法）
        print(f"\n2. 测试 _find_element_at_coordinates 方法:")
        
        # 获取桌面对象
        desktop = uni._get_fresh_desktop()
        
        # 查找打印机应用程序
        printer_app = None
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                if app and app.name == 'system-config-printer':
                    printer_app = app
                    print(f"   找到打印机应用程序: {app.name}")
                    break
            except:
                continue
        
        if printer_app:
            print(f"   调用 _find_element_at_coordinates({x}, {y})...")
            print("-" * 50)
            
            # 直接调用 _find_element_at_coordinates
            element = uni._find_element_at_coordinates(printer_app, x, y)
            
            print("-" * 50)
            print(f"   _find_element_at_coordinates 结果:")
            
            if element:
                name = element.name if element.name else 'unnamed'
                role = element.getRoleName()
                print(f"     控件名称: {name}")
                print(f"     控件角色: {role}")
                
                # 获取控件位置
                try:
                    import pyatspi
                    component = element.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                        print(f"     控件位置: ({extents.x}, {extents.y})")
                        print(f"     控件大小: ({extents.width}, {extents.height})")
                except:
                    pass
                
                if role.lower() == 'page tab list':
                    print(f"     ❌ _find_element_at_coordinates 返回 page tab list")
                    auto_recording_issue = True
                elif '英语' in name or 'english' in name.lower():
                    print(f"     ✅ _find_element_at_coordinates 正确识别为'英语'控件")
                    auto_recording_issue = False
                else:
                    print(f"     ⚠️  _find_element_at_coordinates 识别为其他: {name} ({role})")
                    auto_recording_issue = True
            else:
                print(f"     ❌ _find_element_at_coordinates 未找到控件")
                auto_recording_issue = True
        else:
            print(f"   ❌ 未找到打印机应用程序")
            auto_recording_issue = True
        
        # 3. 测试智能深度搜索
        print(f"\n3. 测试智能深度搜索:")
        
        if printer_app:
            # 获取窗口区域
            try:
                import pyatspi
                component = printer_app.queryComponent()
                if component:
                    extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                    window_region = [extents.x, extents.y, extents.x + extents.width, extents.y + extents.height]
                    
                    print(f"   窗口区域: {window_region}")
                    
                    # 使用智能深度搜索
                    smart_element = uni._smart_deep_search_at_point(printer_app, x, y, window_region)
                    
                    if smart_element:
                        name = smart_element.name if smart_element.name else 'unnamed'
                        role = smart_element.getRoleName()
                        print(f"   智能深度搜索结果: {name} ({role})")
                        
                        if role.lower() == 'page tab list':
                            print(f"   ❌ 智能深度搜索也返回 page tab list")
                            smart_search_issue = True
                        elif '英语' in name or 'english' in name.lower():
                            print(f"   ✅ 智能深度搜索正确识别为'英语'控件")
                            smart_search_issue = False
                        else:
                            print(f"   ⚠️  智能深度搜索识别为其他: {name} ({role})")
                            smart_search_issue = True
                    else:
                        print(f"   ❌ 智能深度搜索未找到控件")
                        smart_search_issue = True
                else:
                    print(f"   ❌ 无法获取窗口区域")
                    smart_search_issue = True
            except Exception as e:
                print(f"   ❌ 智能深度搜索失败: {e}")
                smart_search_issue = True
        else:
            smart_search_issue = True
        
        # 总结
        print(f"\n" + "="*60)
        print("问题分析:")
        print(f"kdk_getElement_Uni 有问题: {'是' if main_method_issue else '否'}")
        print(f"_find_element_at_coordinates 有问题: {'是' if auto_recording_issue else '否'}")
        print(f"智能深度搜索有问题: {'是' if smart_search_issue else '否'}")
        
        if auto_recording_issue:
            print(f"\n🎯 关键问题: _find_element_at_coordinates 方法没有深入搜索到'英语'控件")
            print(f"这就是为什么 auto_recording_manager.py 仍然返回 page tab list 的原因")
        
        return auto_recording_issue
        
    except Exception as e:
        print(f"[ERROR] 调试时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return True

def main():
    """主函数"""
    print("🔍 调试'英语'控件识别问题")
    
    has_issue = debug_english_control()
    
    if has_issue:
        print(f"\n💡 需要进一步修复:")
        print(f"1. 检查 page tab list 深层搜索逻辑")
        print(f"2. 确保搜索能找到'英语'这样的深层控件")
        print(f"3. 可能需要增加搜索深度或改进搜索策略")
    else:
        print(f"\n✅ '英语'控件识别正常")

if __name__ == "__main__":
    main()
