#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试通用 page tab list 修复
"""

import sys
import os

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def simple_test():
    """简单测试"""
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        print(f"简单测试通用 page tab list 修复")
        print("="*50)
        
        # 测试坐标 (501, 417)
        x, y = 501, 417
        
        print(f"测试坐标: ({x}, {y})")
        
        # 测试主要的 kdk_getElement_Uni 方法
        result, info = uni.kdk_getElement_Uni(x, y, False, True)
        
        print(f"结果: {info}")
        if result and not result.get('error'):
            name = result.get('Name', 'Unknown')
            role = result.get('Rolename', 'Unknown')
            print(f"控件: {name} ({role})")
            
            if role.lower() == 'page tab list':
                print(f"❌ 仍然识别为 page tab list，通用修复没有生效")
                return False
            else:
                print(f"✅ 成功！不再识别为 page tab list")
                print(f"识别为: {name} ({role})")
                return True
        else:
            print(f"❌ 识别失败")
            return False
            
    except Exception as e:
        print(f"[ERROR] 测试时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = simple_test()
    
    if success:
        print(f"\n🎉 通用 page tab list 修复成功！")
        print(f"现在 auto_recording_manager.py 应该能正确识别深层控件了")
    else:
        print(f"\n❌ 修复可能不完整")

if __name__ == "__main__":
    main()
