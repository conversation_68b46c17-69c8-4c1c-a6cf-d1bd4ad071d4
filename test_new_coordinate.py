#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新坐标 (750, 444) 的控件识别
"""

import sys
import os
import time
import traceback

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def test_coordinate(x, y):
    """测试指定坐标的控件识别"""
    print(f"\n{'='*80}")
    print(f"测试坐标 ({x}, {y}) 的控件识别")
    print(f"{'='*80}")
    
    try:
        # 创建UNI实例
        uni = UNI()
        os.environ['DISPLAY'] = ':0'
        
        # 1. 使用kdk_getElement_Uni方法测试
        print(f"1. 使用kdk_getElement_Uni方法测试...")
        widget_info, info_text = uni.kdk_getElement_Uni(x, y, False)
        
        print(f"识别结果: {info_text}")
        
        if widget_info and not widget_info.get('error'):
            print(f"✅ 成功识别控件:")
            print(f"  控件名称: {widget_info.get('Name', 'Unknown')}")
            print(f"  控件角色: {widget_info.get('Rolename', 'Unknown')}")
            print(f"  控件坐标: {widget_info.get('Coords', {})}")
            
            # 检查是否是按钮
            if 'button' in widget_info.get('Rolename', '').lower():
                print(f"  🎯 这是一个按钮控件！")
            else:
                print(f"  ❌ 这不是按钮控件: {widget_info.get('Rolename', 'Unknown')}")
        else:
            print(f"❌ 识别失败")
        
        # 2. 检查坐标是否在"添加"按钮范围内
        print(f"\n2. 检查坐标是否在'添加'按钮范围内...")
        # 根据之前的测试，"添加"按钮位置是 (727, 431)，大小是 (58, 32)
        button_x, button_y = 727, 431
        button_w, button_h = 58, 32
        
        in_button_range = (button_x <= x <= button_x + button_w and
                          button_y <= y <= button_y + button_h)
        
        print(f"'添加'按钮范围: x={button_x}-{button_x + button_w}, y={button_y}-{button_y + button_h}")
        print(f"测试坐标: ({x}, {y})")
        print(f"是否在按钮范围内: {in_button_range}")
        
        if not in_button_range:
            print(f"⚠️  坐标 ({x}, {y}) 不在'添加'按钮范围内！")
            print(f"   距离按钮左边界: {x - button_x}")
            print(f"   距离按钮右边界: {(button_x + button_w) - x}")
            print(f"   距离按钮上边界: {y - button_y}")
            print(f"   距离按钮下边界: {(button_y + button_h) - y}")
        
        # 3. 测试智能深度搜索
        print(f"\n3. 测试智能深度搜索...")
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = uni._get_active_window2(x, y)
        
        if active_window:
            print(f"活动窗口: {active_window.name}")
            result = uni._smart_deep_search_at_point(active_window, x, y, activewindow_region)
            
            if result:
                print(f"智能深度搜索结果:")
                print(f"  控件名称: {result.name if result.name else 'unnamed'}")
                print(f"  控件角色: {result.getRoleName()}")
                
                # 获取控件位置
                try:
                    import pyatspi
                    component = result.queryComponent()
                    if component:
                        extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                        print(f"  控件位置: ({extents.x}, {extents.y})")
                        print(f"  控件大小: ({extents.width}, {extents.height})")
                except:
                    pass
            else:
                print("智能深度搜索未找到控件")
        else:
            print("未找到活动窗口")
            
    except Exception as e:
        print(f"[ERROR] 测试时发生错误: {e}")
        traceback.print_exc()

def find_nearby_buttons(x, y):
    """查找附近的按钮控件"""
    print(f"\n{'='*80}")
    print(f"查找坐标 ({x}, {y}) 附近的按钮控件")
    print(f"{'='*80}")
    
    try:
        import pyatspi
        
        # 创建UNI实例
        uni = UNI()
        os.environ['DISPLAY'] = ':0'
        
        # 获取桌面对象
        desktop = uni._get_fresh_desktop()
        if not desktop:
            print("无法获取桌面对象")
            return
        
        # 查找打印机应用程序
        printer_app = None
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                if app and app.name == 'system-config-printer':
                    printer_app = app
                    break
            except:
                continue
                
        if not printer_app:
            print("未找到打印机应用程序")
            return
        
        # 查找所有按钮控件
        def find_buttons(widget, depth=0, max_depth=10):
            if depth > max_depth:
                return []
                
            found_buttons = []
            
            try:
                role = widget.getRoleName().lower()
                name = widget.name if widget.name else 'unnamed'
                
                if 'button' in role:
                    try:
                        component = widget.queryComponent()
                        if component:
                            extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                            
                            # 计算距离目标坐标的距离
                            center_x = extents.x + extents.width // 2
                            center_y = extents.y + extents.height // 2
                            distance = ((center_x - x) ** 2 + (center_y - y) ** 2) ** 0.5
                            
                            found_buttons.append({
                                'name': name,
                                'role': role,
                                'position': (extents.x, extents.y),
                                'size': (extents.width, extents.height),
                                'center': (center_x, center_y),
                                'distance': distance,
                                'depth': depth
                            })
                    except:
                        pass
                
                # 递归搜索子控件
                for i in range(widget.childCount):
                    try:
                        child = widget.getChildAtIndex(i)
                        if child:
                            child_buttons = find_buttons(child, depth + 1, max_depth)
                            found_buttons.extend(child_buttons)
                    except:
                        continue
                        
            except:
                pass
                
            return found_buttons
        
        buttons = find_buttons(printer_app)
        if buttons:
            # 按距离排序
            buttons.sort(key=lambda b: b['distance'])
            
            print(f"找到 {len(buttons)} 个按钮控件（按距离排序）:")
            for i, button in enumerate(buttons):
                print(f"{i+1}. '{button['name']}' ({button['role']})")
                print(f"   位置: {button['position']} 大小: {button['size']}")
                print(f"   中心: {button['center']} 距离: {button['distance']:.1f}px")
                
                # 检查是否包含目标坐标
                x1, y1 = button['position']
                w, h = button['size']
                if (x1 <= x <= x1 + w and y1 <= y <= y1 + h):
                    print(f"   ✅ 包含坐标 ({x}, {y})")
                print()
        else:
            print("未找到按钮控件")
            
    except Exception as e:
        print(f"[ERROR] 查找按钮时发生错误: {e}")
        traceback.print_exc()

def main():
    """主函数"""
    # 测试新坐标
    test_coordinate(750, 444)
    
    # 查找附近的按钮
    find_nearby_buttons(750, 444)

if __name__ == "__main__":
    main()
