#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新坐标 (410, 355) 的控件识别
"""

import sys
import os

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def test_new_coordinates():
    """测试新坐标的控件识别"""
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        # 新的测试坐标
        x, y = 410, 355
        
        print(f"测试新坐标的控件识别")
        print(f"坐标: ({x}, {y})")
        print("="*60)
        
        # 1. 测试 kdk_getElement_Uni 方法
        print(f"1. 测试 kdk_getElement_Uni 方法:")
        result, info = uni.kdk_getElement_Uni(x, y, False, True)
        
        print(f"   结果: {info}")
        if result and not result.get('error'):
            name = result.get('Name', 'Unknown')
            role = result.get('Rolename', 'Unknown')
            coords = result.get('Coords', {})
            print(f"   控件: {name} ({role})")
            print(f"   坐标: {coords}")
            
            if 'button' in role.lower():
                if '添加' in name:
                    print(f"   ✅ 正确识别为'添加'按钮！")
                    return True
                else:
                    print(f"   ⚠️  识别为其他按钮: {name}")
            elif role.lower() == 'page tab list':
                print(f"   ❌ 仍然识别为 page tab list")
            else:
                print(f"   ℹ️  识别为其他控件: {name} ({role})")
        else:
            print(f"   ❌ 识别失败: {info}")
        
        # 2. 测试 auto_recording_manager.py 使用的方法
        print(f"\n2. 测试 auto_recording_manager.py 使用的方法:")
        
        # 获取桌面对象
        desktop = uni._get_fresh_desktop()
        
        # 查找打印机应用程序
        printer_app = None
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                if app and app.name == 'system-config-printer':
                    printer_app = app
                    print(f"   找到打印机应用程序: {app.name}")
                    break
            except:
                continue
        
        if printer_app:
            # 测试警告窗口专项检测
            print(f"   测试警告窗口专项检测...")
            warning_element = uni._find_warning_dialog_at_point(x, y)
            
            if warning_element:
                name = warning_element.name if warning_element.name else 'unnamed'
                role = warning_element.getRoleName()
                print(f"   警告窗口检测结果: {name} ({role})")
                
                if 'button' in role.lower() and '添加' in name:
                    print(f"   ✅ 警告窗口检测正确识别为'添加'按钮！")
                    return True
                elif role.lower() == 'page tab list':
                    print(f"   ❌ 警告窗口检测识别为 page tab list")
                else:
                    print(f"   ⚠️  警告窗口检测识别为其他: {name} ({role})")
            else:
                print(f"   ℹ️  警告窗口检测未找到控件")
            
            # 测试 _find_element_at_coordinates
            print(f"   测试 _find_element_at_coordinates...")
            element = uni._find_element_at_coordinates(printer_app, x, y)
            
            if element:
                name = element.name if element.name else 'unnamed'
                role = element.getRoleName()
                print(f"   _find_element_at_coordinates 结果: {name} ({role})")
                
                if 'button' in role.lower() and '添加' in name:
                    print(f"   ✅ _find_element_at_coordinates 正确识别为'添加'按钮！")
                    return True
                elif role.lower() == 'page tab list':
                    print(f"   ❌ _find_element_at_coordinates 识别为 page tab list")
                else:
                    print(f"   ⚠️  _find_element_at_coordinates 识别为其他: {name} ({role})")
            else:
                print(f"   ❌ _find_element_at_coordinates 未找到控件")
        else:
            print(f"   ❌ 未找到打印机应用程序")
        
        return False
        
    except Exception as e:
        print(f"[ERROR] 测试时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 测试新坐标 (410, 355) 的控件识别")
    
    success = test_new_coordinates()
    
    if success:
        print(f"\n🎉 新坐标识别成功！")
        print(f"现在请用坐标 (410, 355) 测试 auto_recording_manager.py")
    else:
        print(f"\n❌ 新坐标识别失败")
        print(f"可能需要进一步调试或确认坐标是否正确")

if __name__ == "__main__":
    main()
