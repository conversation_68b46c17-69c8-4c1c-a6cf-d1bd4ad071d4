#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证 auto_recording_manager.py 修复效果
"""

import sys
import os

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def verify_fix():
    """验证修复效果"""
    print(f"验证 auto_recording_manager.py 修复效果")
    print(f"{'='*50}")
    
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        # 测试坐标
        x, y = 458, 330
        
        print(f"测试坐标: ({x}, {y})")
        
        # 1. 测试 _is_interactive_control 修复
        print(f"\n1. 测试 _is_interactive_control 修复:")
        page_tab_list_interactive = uni._is_interactive_control('page tab list')
        button_interactive = uni._is_interactive_control('push button')
        
        print(f"   'page tab list' 是交互控件: {page_tab_list_interactive}")
        print(f"   'push button' 是交互控件: {button_interactive}")
        
        if not page_tab_list_interactive and button_interactive:
            print(f"   ✅ _is_interactive_control 修复成功")
        else:
            print(f"   ❌ _is_interactive_control 修复失败")
        
        # 2. 测试 _find_element_at_coordinates 方法
        print(f"\n2. 测试 _find_element_at_coordinates 方法:")
        
        # 获取桌面对象
        desktop = uni._get_fresh_desktop()
        
        # 查找打印机应用程序
        printer_app = None
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                if app and app.name == 'system-config-printer':
                    printer_app = app
                    break
            except:
                continue
        
        if printer_app:
            print(f"   找到打印机应用程序")
            
            # 直接调用 _find_element_at_coordinates
            element = uni._find_element_at_coordinates(printer_app, x, y)
            
            if element:
                name = element.name if element.name else 'unnamed'
                role = element.getRoleName()
                print(f"   识别结果: {name} ({role})")
                
                if 'button' in role.lower() and '添加' in name:
                    print(f"   ✅ _find_element_at_coordinates 修复成功！")
                    return True
                elif role.lower() == 'page tab list':
                    print(f"   ❌ _find_element_at_coordinates 仍然识别为 page tab list")
                    return False
                else:
                    print(f"   ⚠️  _find_element_at_coordinates 识别为其他: {name} ({role})")
                    return False
            else:
                print(f"   ❌ _find_element_at_coordinates 未找到控件")
                return False
        else:
            print(f"   ❌ 未找到打印机应用程序")
            return False
            
    except Exception as e:
        print(f"[ERROR] 验证时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = verify_fix()
    
    if success:
        print(f"\n🎉 修复验证成功！")
        print(f"auto_recording_manager.py 现在应该能正确识别按钮控件了")
        print(f"请重新测试您的 auto_recording_manager.py")
    else:
        print(f"\n❌ 修复验证失败")
        print(f"可能需要进一步调试")

if __name__ == "__main__":
    main()
