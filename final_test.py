#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终测试修复效果
"""

import sys
import os

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def final_test():
    """最终测试"""
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        # 测试坐标
        x, y = 458, 330
        
        print(f"最终测试修复效果")
        print(f"坐标: ({x}, {y})")
        print("="*50)
        
        # 测试主要的控件识别方法
        print(f"测试 kdk_getElement_Uni 方法:")
        result, info = uni.kdk_getElement_Uni(x, y, False, True)
        
        print(f"结果: {info}")
        if result and not result.get('error'):
            name = result.get('Name', 'Unknown')
            role = result.get('Rolename', 'Unknown')
            print(f"控件: {name} ({role})")
            
            if 'button' in role.lower() and '添加' in name:
                print(f"✅ 修复成功！正确识别为'添加'按钮")
                print(f"\n🎉 auto_recording_manager.py 现在应该能正确识别按钮控件了！")
                print(f"请重新测试您的 auto_recording_manager.py")
                return True
            elif role.lower() == 'page tab list':
                print(f"❌ 仍然识别为 page tab list")
            else:
                print(f"⚠️  识别为其他: {name} ({role})")
        else:
            print(f"❌ 识别失败")
        
        return False
        
    except Exception as e:
        print(f"[ERROR] 测试时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = final_test()
    
    if not success:
        print(f"\n❌ 修复可能不完整")
        print(f"建议：")
        print(f"1. 重启 auto_recording_manager.py 程序")
        print(f"2. 确认测试坐标是否正确")
        print(f"3. 检查'添加'按钮是否在屏幕上可见")

if __name__ == "__main__":
    main()
