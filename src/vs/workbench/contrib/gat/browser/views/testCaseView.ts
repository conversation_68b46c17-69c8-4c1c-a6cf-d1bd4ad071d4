/*---------------------------------------------------------------------------------------------
 *  Copyright (c) KylinRobot Team. All rights reserved.
 *  Licensed under the MIT License.
 *--------------------------------------------------------------------------------------------*/

import { localize, localize2 } from '../../../../../nls.js';
import { ViewPane } from '../../../../browser/parts/views/viewPane.js';
import { IViewletViewOptions } from '../../../../browser/parts/views/viewsViewlet.js';
import { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { IContextMenuService } from '../../../../../platform/contextview/browser/contextView.js';
import { IKeybindingService } from '../../../../../platform/keybinding/common/keybinding.js';
import { IContextKeyService } from '../../../../../platform/contextkey/common/contextkey.js';
import { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';
import { IOpenerService } from '../../../../../platform/opener/common/opener.js';
import { IThemeService } from '../../../../../platform/theme/common/themeService.js';
import { IStorageService } from '../../../../../platform/storage/common/storage.js';
import * as DOM from '../../../../../base/browser/dom.js';
import { IHoverService } from '../../../../../platform/hover/browser/hover.js';
import { IViewDescriptorService } from '../../../../common/views.js';
import { IAccessibleViewInformationService } from '../../../../services/accessibility/common/accessibleViewInformationService.js';
import { IListVirtualDelegate } from '../../../../../base/browser/ui/list/list.js';
import { List } from '../../../../../base/browser/ui/list/listWidget.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { Emitter, Event } from '../../../../../base/common/event.js';
import { URI } from '../../../../../base/common/uri.js';
import { joinPath, dirname } from '../../../../../base/common/resources.js';
import { IEditorService } from '../../../../services/editor/common/editorService.js';
import { IFileService } from '../../../../../platform/files/common/files.js';
import { IWorkspaceContextService } from '../../../../../platform/workspace/common/workspace.js';
import { IZenTaoService } from '../../common/zentaoService.js';
import { TextFormatCleaner } from '../utils/textFormatCleaner.js';
import { Button } from '../../../../../base/browser/ui/button/button.js';
import { IQuickInputService, IQuickPickItem } from '../../../../../platform/quickinput/common/quickInput.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { ITextFileService } from '../../../../services/textfile/common/textfiles.js';
import { IWebviewService } from '../../../../../workbench/contrib/webview/browser/webview.js';
import { IWebviewWorkbenchService } from '../../../../../workbench/contrib/webviewPanel/browser/webviewWorkbenchService.js';
import { ICustomAuthenticationService } from '../../../../../platform/authentication/common/customAuthenticationService.js';
import '../features/lib/vendor/js-yaml.js'; // 导入 vendored js-yaml
import { GATPathResolver } from '../common/pathResolver.js';

/**
 * 测试用例接口
 */
export interface TestCase {
    id: string;
    name: string;
    story?: string;
    ymlPath?: string;
    [key: string]: any;
}

/**
 * 测试用例数据接口
 */
interface TestCaseData {
    id: string;           // 测试用例ID
    name: string;         // 测试用例名称
    module: string;       // 测试用例模块
    recordType: string;   // 录制类型
    directRecord: boolean; // 是否直接录制
    manualVerification: boolean; // 是否人工校验
    author?: string;      // 作者（可选）
    driver?: string;      // 驱动应用（可选）
    testSetName: string;  // 所属测试集
    steps?: Array<{ index: number | string; desc: string; expect: string }>; // 用例步骤 (修改后，数组格式)
    customParameters?: Array<{ key: string; value: string; type: string }>; // 自定义参数
}

/**
 * 测试用例模板数据
 */
interface TestCaseTemplateData {
    container: HTMLElement;
    id: HTMLElement;
    name: HTMLElement;
    story: HTMLElement;
}

export class TestCaseView extends ViewPane {

    static readonly ID = 'workbench.views.gat.testCaseView';
    static readonly TITLE = localize2('testCase', "测试用例");

    private readonly _onDidChangeSelection = this._register(new Emitter<TestCase>());
    readonly onDidChangeSelection: Event<TestCase> = this._onDidChangeSelection.event;

    private readonly _testCaseElement: HTMLElement;
    private placeholderElement: HTMLElement;
    private testCaseList!: List<TestCase>;
    private _testCases: TestCase[] = [];
    private listContainer: HTMLElement;
    private addTestCaseButton: Button | undefined;
    private currentTestSetName: string = '';
    private currentTestSetPath: URI | undefined;
    private isRecording: boolean = false;
    private currentRecordingCaseId: string | null = null;
    private readonly pathResolver: GATPathResolver;

    /**
     * 测试用例选择事件发射器
     */
    private readonly _onTestCaseSelected = new Emitter<TestCase>();

    /**
     * 测试用例选择事件
     */
    public readonly onTestCaseSelected: Event<TestCase> = this._onTestCaseSelected.event;

    /**
     * 获取当前测试用例列表
     */
    public get testCases(): TestCase[] {
        return this._testCases;
    }

    constructor(
        options: IViewletViewOptions,
        @IKeybindingService keybindingService: IKeybindingService,
        @IContextMenuService contextMenuService: IContextMenuService,
        @IConfigurationService protected override readonly configurationService: IConfigurationService,
        @IContextKeyService contextKeyService: IContextKeyService,
        @IViewDescriptorService viewDescriptorService: IViewDescriptorService,
        @IInstantiationService protected override readonly instantiationService: IInstantiationService,
        @IOpenerService openerService: IOpenerService,
        @IThemeService themeService: IThemeService,
        @IHoverService hoverService: IHoverService,
        @IStorageService storageService: IStorageService,
        @ILogService private readonly logService: ILogService,
        @IAccessibleViewInformationService accessibleViewInformationService: IAccessibleViewInformationService,
        @IEditorService private readonly editorService: IEditorService,
        @IFileService private readonly fileService: IFileService,
        @IWorkspaceContextService private readonly workspaceContextService: IWorkspaceContextService,
        @IQuickInputService private readonly quickInputService: IQuickInputService,
        @INotificationService private readonly notificationService: INotificationService,
        @ITextFileService private readonly textFileService: ITextFileService,
        @IWebviewService _webviewService: IWebviewService,
        @IWebviewWorkbenchService private readonly webviewWorkbenchService: IWebviewWorkbenchService,
        @IZenTaoService private readonly zenTaoService: IZenTaoService,
        @ICustomAuthenticationService private readonly customAuthenticationService: ICustomAuthenticationService,
    ) {
        super(
            options,
            keybindingService,
            contextMenuService,
            configurationService,
            contextKeyService,
            viewDescriptorService,
            instantiationService,
            openerService,
            themeService,
            hoverService,
            accessibleViewInformationService
        );

        // 初始化路径解析器
        this.pathResolver = new GATPathResolver(
            this.configurationService,
            this.workspaceContextService,
            this.fileService,
            this.logService
        );

        // 创建测试用例容器
        this._testCaseElement = document.createElement('div');
        this._testCaseElement.className = 'test-case-container';
        this._testCaseElement.style.height = '100%';

        // 创建占位符
        this.placeholderElement = document.createElement('div');
        this.placeholderElement.className = 'test-case-placeholder';
        this.placeholderElement.textContent = localize('testCasePlaceholder', "选择测试集后显示相关测试用例");
        this._testCaseElement.appendChild(this.placeholderElement);

        // 创建列表容器
        this.listContainer = document.createElement('div');
        this.listContainer.className = 'test-case-list-container';
        this.listContainer.style.height = '100%';
        this.listContainer.style.overflow = 'auto';
        this.listContainer.style.flex = '1';
        this.listContainer.style.width = '100%';
        this.listContainer.style.minWidth = 'unset';
        this._testCaseElement.appendChild(this.listContainer);

        // 创建测试用例列表
        this.createTestCaseList();

        // 监听DOM事件
        this.setupDOMEventListeners();

        // 设置初始状态
        this.updateListVisibility();

        // 注册自定义事件监听器，用于接收测试用例更新
        this.logService.info('在构造函数中注册事件监听器');

        const listener = (e: CustomEvent) => {
            this.logService.info('构造函数中的监听器收到gat:testcases-updated事件');

            try {
                if (e.detail && e.detail.testCases) {
                    this.logService.info(`接收到测试用例更新事件: ${e.detail.testCases.length}`);

                    if (e.detail.testCases.length > 0) {
                        this.logService.info(`测试用例示例: ${JSON.stringify(e.detail.testCases[0])}`);
                    }

                    // 设置当前测试集名称
                    if (e.detail.testSetName) {
                        this.setCurrentTestSetName(e.detail.testSetName);
                    }

                    // 设置当前测试集路径
                    if (e.detail.testSetPath) {
                        this.setCurrentTestSetPath(URI.parse(e.detail.testSetPath));
                        this.logService.info(`从事件中设置测试集路径: ${e.detail.testSetPath}`);
                    }

                    // 设置测试用例
                    this.setTestCases(e.detail.testCases);
                }
            } catch (error) {
                this.logService.error(`处理测试用例更新事件时出错: ${error instanceof Error ? error.message : String(error)}`);
            }
        };

        this._register(DOM.addDisposableListener(document, 'gat:testcases-updated', listener));
        this.logService.info('已在构造函数中注册事件监听器');

        // 监听录制状态变化，更新按钮状态
        this._register(DOM.addDisposableListener(document, 'gat:recording-state-changed', (e: any) => {
            this.isRecording = e.detail.isRecording;
            this.currentRecordingCaseId = e.detail.currentTestCaseId;
            this.updateRecordButtons();
        }));

        // 添加配置变更监听器，当gat.testcasePath配置变更时清空当前状态
        this._register(this.configurationService.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('gat.testcasePath')) {
                this.logService.info('检测到gat.testcasePath配置变更，清空测试用例视图状态');
                this.onTestcasePathConfigurationChanged();
            }
        }));
    }

    /**
     * 处理testcase路径配置变更
     */
    private onTestcasePathConfigurationChanged(): void {
        // 当testcase路径配置变更时，清空当前的测试用例状态，等待用户重新选择测试集
        this.currentTestSetPath = undefined;
        this.currentTestSetName = '';
        this.clearTestCases();
        this.updateListVisibility();
        this.logService.info('已清空测试用例视图状态，等待重新选择测试集');
    }

    /**
     * 创建测试用例列表
     */
    private createTestCaseList(): void {
        this.logService.info('开始创建测试用例列表');

        // 检查列表容器是否存在
        if (!this.listContainer) {
            this.logService.error('列表容器不存在');
            return;
        }
        // 创建列表委托
        const delegate = new class implements IListVirtualDelegate<TestCase> {
            getHeight(element: TestCase): number {
                return 30;
            }

            getTemplateId(): string {
                return 'testCaseTemplate';
            }
        };

        // 创建列表渲染器
        const renderer = {
            templateId: 'testCaseTemplate',
            renderTemplate: (parent: HTMLElement): TestCaseTemplateData => {
                const container = DOM.append(parent, DOM.$('.test-case-item'));

                // 设置列表项样式
                container.style.display = 'flex';
                container.style.padding = '4px 12px';
                container.style.borderBottom = '1px solid var(--vscode-list-inactiveSelectionBackground)';
                container.style.justifyContent = 'space-between'; // 左右两端对齐
                container.style.alignItems = 'center'; // 垂直居中
                container.style.width = '100%'; // 确保宽度100%
                container.style.boxSizing = 'border-box'; // 确保padding不增加总宽度

                // 创建ID元素
                const id = DOM.append(container, DOM.$('.test-case-id'));
                id.style.fontWeight = 'bold';
                id.style.overflow = 'hidden';
                id.style.textOverflow = 'ellipsis';
                id.style.whiteSpace = 'nowrap';
                id.style.marginRight = '12px';
                id.style.maxWidth = '65%';

                // 创建操作按钮容器
                const actionContainer = DOM.append(container, DOM.$('.test-case-actions'));
                actionContainer.style.display = 'flex';
                actionContainer.style.gap = '8px';
                actionContainer.style.flexShrink = '0';
                actionContainer.style.minWidth = '90px';
                actionContainer.style.justifyContent = 'flex-end';

                // 创建录制按钮
                const recordButton = DOM.append(actionContainer, DOM.$('.test-case-action-record'));
                recordButton.title = '录制';
                recordButton.classList.add('codicon', 'codicon-record');
                recordButton.style.cursor = 'pointer';
                recordButton.style.minWidth = '24px';
                recordButton.style.flexShrink = '0';

                // 创建播放按钮
                const playButton = DOM.append(actionContainer, DOM.$('.test-case-action-play'));
                playButton.title = '播放';
                playButton.classList.add('codicon', 'codicon-play');
                playButton.style.cursor = 'pointer';
                playButton.style.minWidth = '24px';
                playButton.style.flexShrink = '0';

                // 创建删除按钮
                const deleteButton = DOM.append(actionContainer, DOM.$('.test-case-action-delete'));
                deleteButton.title = '删除';
                deleteButton.classList.add('codicon', 'codicon-trash');
                deleteButton.style.cursor = 'pointer';
                deleteButton.style.minWidth = '24px';
                deleteButton.style.flexShrink = '0';

                // 为了保留原有的name和story元素，但不显示在UI上
                const name = document.createElement('span');
                name.style.display = 'none';

                const story = document.createElement('span');
                story.style.display = 'none';

                return { container, id, name, story };
            },
            renderElement: (element: TestCase, index: number, templateData: TestCaseTemplateData): void => {
                // 设置测试用例数据
                templateData.id.textContent = element.id;
                templateData.name.textContent = element.name;
                templateData.story.textContent = element.story || ''; // 防止undefined

                // 设置tooltip (鼠标悬停提示)
                // 介新增：增强作者信息的获取，从多个可能的字段获取作者信息
                // 1. 直接从element.author获取
                // 2. 从element.TestCaseAuthor或testCaseData中的TestCaseAuthor获取
                // 3. 从YAML文件中解析的author字段获取
                // 4. 如果以上均不存在，则使用默认值

                let author = element.author ||
                    element.TestCaseAuthor ||
                    element.testCaseAuthor ||
                    (element as any)?.['author'] ||
                    '未指定';

                // 可能的情况：作者字段可能在原始数据对象的其他属性中
                if (author === '未指定' && typeof element === 'object') {
                    // 遍历所有属性，查找可能包含作者信息的字段
                    for (const key in element) {
                        const lowerKey = key.toLowerCase();
                        if (lowerKey.includes('author') && element[key]) {
                            author = element[key];
                            break;
                        }
                    }
                }

                // 记录寻找作者信息的过程并输出实际的element结构
                this.logService.debug(`测试用例[${element.id}]作者信息获取: ${author}`);
                if (author === '未指定') {
                    // 当作者信息不存在时，输出对象结构以便调试
                    this.logService.debug(`测试用例完整数据: ${JSON.stringify(element, null, 2)}`);
                }

                // 设置新的悬停提示，包含名称和作者信息
                let tooltipText = `名称: ${element.name}\n作者: ${author}`;
                if (templateData.story.textContent) {
                    tooltipText += `\n描述: ${templateData.story.textContent}`;
                }
                templateData.container.title = tooltipText;

                // 添加列表项悬停样式
                const container = templateData.container;
                container.addEventListener('mouseover', () => {
                    container.style.backgroundColor = 'var(--vscode-list-hoverBackground)';
                });
                container.addEventListener('mouseout', () => {
                    container.style.backgroundColor = '';
                });

                // 获取操作按钮
                const recordButton = container.querySelector('.test-case-action-record');
                const playButton = container.querySelector('.test-case-action-play');
                const deleteButton = container.querySelector('.test-case-action-delete');

                // 🔧 修复：每次渲染时都清除旧的事件监听器并重新绑定
                // 这样确保事件监听器始终绑定到当前正确的 element 对象
                if (recordButton) {
                    // 移除旧的事件监听器标记，强制重新绑定
                    recordButton.removeAttribute('data-gat-record-listener');
                    // 克隆节点来彻底清除所有事件监听器
                    const newRecordButton = recordButton.cloneNode(true) as HTMLElement;
                    recordButton.parentNode?.replaceChild(newRecordButton, recordButton);

                    // 重新绑定事件监听器到当前element
                    newRecordButton.addEventListener('click', (e) => {
                        e.stopPropagation(); // 阻止事件冒泡，避免触发列表项选择
                        this.logService.info(`录制测试用例: ${element.id}`);
                        // this.recordTestCase(element); // 旧的直接调用
                        this.promptRecordModeAndRecordTestCase(element); // 改为调用新的方法
                    });
                }

                if (playButton) {
                    // 移除旧的事件监听器标记，强制重新绑定
                    playButton.removeAttribute('data-gat-play-listener');
                    // 克隆节点来彻底清除所有事件监听器
                    const newPlayButton = playButton.cloneNode(true) as HTMLElement;
                    playButton.parentNode?.replaceChild(newPlayButton, playButton);

                    // 重新绑定事件监听器到当前element
                    newPlayButton.addEventListener('click', (e) => {
                        e.stopPropagation(); // 阻止事件冒泡，避免触发列表项选择
                        this.logService.info(`播放测试用例: ${element.id}`);
                        this.playTestCase(element);
                    });
                }

                if (deleteButton) {
                    // 克隆节点来彻底清除所有事件监听器
                    const newDeleteButton = deleteButton.cloneNode(true) as HTMLElement;
                    deleteButton.parentNode?.replaceChild(newDeleteButton, deleteButton);

                    // 重新绑定事件监听器到当前element
                    newDeleteButton.addEventListener('click', (e) => {
                        e.stopPropagation(); // 阻止事件冒泡，避免触发列表项选择
                        this.logService.info(`删除测试用例: ${element.id}`);
                        this.deleteTestCase(element);
                    });
                }

                // 如果是OCR类型测试用例，或指定了YAML路径，则从YAML文件中解析story和author
                if ((element as any).recordType?.toLowerCase() === 'ocr' || element.ymlPath) {
                    (async () => {
                        try {
                            let yamlUri: URI;
                            if (element.ymlPath && typeof element.ymlPath === 'string') { // 检查 ymlPath 是否已经是字符串
                                try {
                                    yamlUri = URI.parse(element.ymlPath as string);
                                } catch (parseError) {
                                    this.logService.warn(`无法解析已存在的 ymlPath: ${element.ymlPath}，将尝试查找。错误: ${parseError}`);
                                    // 如果解析失败，则尝试重新查找
                                    const foundUri = await this.findPathAndSet(element); // 辅助方法查找并设置路径
                                    if (!foundUri) return;
                                    yamlUri = foundUri;
                                }
                            } else {
                                const foundUri = await this.findPathAndSet(element); // 辅助方法查找并设置路径
                                if (!foundUri) return;
                                yamlUri = foundUri;
                            }

                            const fileContent = await this.fileService.readFile(yamlUri);
                            const content = fileContent.value.toString();
                            // 简单解析story和author字段
                            const storyMatch = content.match(/^\s*story:\s*(.*)$/m);
                            const authorMatch = content.match(/^\s*author:\s*(.*)$/m);
                            if (storyMatch) {
                                templateData.story.textContent = storyMatch[1];
                            }
                            let finalAuthor = author;
                            if (authorMatch) {
                                finalAuthor = authorMatch[1];
                            }
                            // 更新tooltip
                            let newTooltip = `名称: ${element.name}\n作者: ${finalAuthor}`;
                            if (templateData.story.textContent) {
                                newTooltip += `\n描述: ${templateData.story.textContent}`;
                            }
                            templateData.container.title = newTooltip;

                            // 在所有操作成功后，如果 ymlPath 之前不是字符串或为空，则进行更新
                            // 确保此时 yamlUri 是有效的，并且 element.ymlPath 被更新为字符串路径
                            if (yamlUri && (typeof element.ymlPath !== 'string' || !element.ymlPath)) {
                                element.ymlPath = yamlUri.toString();
                                this.logService.info(`(OCR/ymlPath分支) 已更新测试用例 ${element.id} 的 ymlPath 为: ${element.ymlPath}`);
                            }

                        } catch (error) {
                            this.logService.error(`从YAML加载信息失败: ${error instanceof Error ? error.message : String(error)}`);
                        }
                    })();
                }
            },
            disposeTemplate: (templateData: TestCaseTemplateData): void => {
                // 清理资源
            }
        };

        // 创建测试用例列表
        try {
            this.logService.info('开始创建 List 对象');
            this.testCaseList = new List<TestCase>(
                'TestCases',
                this.listContainer,
                delegate,
                [renderer],
                {
                    keyboardSupport: true,
                    mouseSupport: true,
                    multipleSelectionSupport: false // 只允许单选
                }
            );
            this.logService.info('List 对象创建成功');

            // 注册列表选择事件
            this.testCaseList.onDidChangeSelection(e => {
                if (e.elements.length === 1) {
                    const selectedTestCase = e.elements[0];
                    this.logService.info(`测试用例被选中: ${selectedTestCase.id} - ${selectedTestCase.name}`);
                    this._onTestCaseSelected.fire(selectedTestCase);
                    this._onDidChangeSelection.fire(selectedTestCase);

                    // 触发测试用例选择事件
                    this.fireTestCaseSelectedEvent(selectedTestCase);
                }
            });

            // 初始状态下隐藏列表
            this.listContainer.style.display = 'none';
            this.logService.info('测试用例列表创建完成，初始状态为隐藏');
        } catch (error) {
            this.logService.error(`创建测试用例列表时出错: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * 设置DOM事件监听器
     */
    private setupDOMEventListeners(): void {
        this.logService.info('开始设置DOM事件监听器');

        // 监听测试用例更新事件
        const listener = (e: any) => {
            this.logService.info('收到gat:testcases-updated事件');

            try {
                if (e.detail && e.detail.testCases) {
                    this.logService.info(`收到测试用例更新事件，包含 ${e.detail.testCases.length} 个测试用例`);
                    this.logService.info(`测试集名称: ${e.detail.testSetName || '未指定'}`);

                    if (e.detail.testCases.length > 0) {
                        this.logService.info(`测试用例示例: ${JSON.stringify(e.detail.testCases[0])}`);
                    }

                    // 设置当前测试集名称
                    if (e.detail.testSetName) {
                        this.setCurrentTestSetName(e.detail.testSetName);
                    }

                    // 设置测试用例
                    this.setTestCases(e.detail.testCases);

                    // 强制刷新UI
                    setTimeout(() => {
                        this.updateListVisibility();
                        this.logService.info('强制刷新UI完成');
                    }, 100);
                } else {
                    this.logService.warn('收到测试用例更新事件，但缺少测试用例数据');
                }
            } catch (error) {
                this.logService.error(`处理测试用例更新事件时出错: ${error instanceof Error ? error.message : String(error)}`);
            }
        };

        document.addEventListener('gat:testcases-updated', listener);
        this.logService.info('已添加gat:testcases-updated事件监听器');

        // 添加到清理列表
        this._register({
            dispose: () => {
                document.removeEventListener('gat:testcases-updated', listener);
                this.logService.info('已移除gat:testcases-updated事件监听器');
            }
        });
    }

    /**
     * 更新列表的可见性
     */
    private updateListVisibility(): void {
        this.logService.info(`更新列表可见性，当前测试用例数量: ${this._testCases.length}`);

        // 检查占位符元素是否存在
        if (!this.placeholderElement) {
            this.logService.error('占位符元素不存在');
            return;
        }

        // 检查列表容器是否存在
        if (!this.listContainer) {
            this.logService.error('列表容器不存在');
            return;
        }

        if (this._testCases.length > 0) {
            // 显示列表，隐藏占位符
            this.placeholderElement.style.display = 'none';
            this.listContainer.style.display = 'block';
            this.logService.info('已设置列表可见，占位符隐藏');

            // 确保列表可见
            this.listContainer.classList.add('debug-visible');
            if (this.testCaseList) {
                const containerHeight = this._testCaseElement.clientHeight;
                this.logService.info(`设置列表高度: ${containerHeight}px`);
                this.testCaseList.layout(containerHeight);
                this.testCaseList.domFocus();
            } else {
                this.logService.error('测试用例列表对象不存在');
            }

            this.logService.info(`测试用例列表现在应该可见，有 ${this._testCases.length} 个测试用例`);
        } else {
            // 隐藏列表，显示占位符
            this.placeholderElement.style.display = 'block';
            this.listContainer.style.display = 'none';

            this.logService.info('没有测试用例，显示占位符');
        }
    }

    /**
     * 触发测试用例选择事件
     */
    private fireTestCaseSelectedEvent(testCase: TestCase): void {
        // 创建自定义事件
        const event = new CustomEvent('gat:testcase-selected', {
            detail: { testCase },
            bubbles: true,
            cancelable: true
        });

        document.dispatchEvent(event);
        this.logService.debug(`测试用例选择事件已触发: ${testCase.id}`);
    }

    /**
     * 设置测试用例
     */
    public setTestCases(testCases: TestCase[]): void {
        this.logService.info(`设置 ${testCases.length} 个测试用例到视图中`);

        // 检查测试用例数组是否有效
        if (!Array.isArray(testCases)) {
            this.logService.error('无效的测试用例数组');
            return;
        }

        // 记录详细测试用例信息用于调试
        testCases.forEach((tc, index) => {
            if (index < 5) { // 只记录前5个，避免日志过多
                this.logService.info(`测试用例 ${index}: id=${tc.id}, name=${tc.name}`);
            }
        });

        this._testCases = testCases;

        // 检查测试用例列表是否存在
        if (!this.testCaseList) {
            this.logService.error('测试用例列表对象不存在，无法更新数据');
            // 尝试重新创建列表
            this.createTestCaseList();
        }

        // 更新列表数据
        if (this.testCaseList) {
            this.logService.info('开始更新列表数据');
            try {
                this.testCaseList.splice(0, this.testCaseList.length, testCases);
                this.logService.info('列表数据更新成功');
            } catch (error) {
                this.logService.error(`更新列表数据时出错: ${error instanceof Error ? error.message : String(error)}`);
            }
        }

        // 更新UI状态
        this.updateListVisibility();

        // 强制布局刷新
        setTimeout(() => {
            if (this.testCaseList && testCases.length > 0) {
                try {
                    const height = this._testCaseElement.clientHeight;
                    this.logService.info(`延时刷新列表布局，高度: ${height}px`);
                    this.testCaseList.layout(height);
                } catch (error) {
                    this.logService.error(`刷新列表布局时出错: ${error instanceof Error ? error.message : String(error)}`);
                }
            }
        }, 100);
    }

    /**
     * 获取当前测试用例列表
     */
    public getTestCases(): TestCase[] {
        return this._testCases;
    }

    /**
     * 清除测试用例列表
     */
    public clearTestCases(): void {
        this.setTestCases([]);
    }

    /**
     * 渲染视图标题描述
     */
    protected override renderHeader(container: HTMLElement): void {
        // 先调用父类的renderHeader方法
        super.renderHeader(container);

        // 然后添加我们的增强样式类
        container.classList.add('test-case-enhanced-header');

        // 添加actions-always-visible类，确保操作按钮始终可见
        container.classList.add('actions-always-visible');

        // 创建按钮容器
        // 使用类型断言确保元素是HTMLElement类型
        const actionsContainer = (container.querySelector('.actions-container') as HTMLElement) || container;

        // 创建新增测试用例按钮
        this.addTestCaseButton = new Button(actionsContainer, {
            title: localize('addTestCase', '新建测试用例'),
            supportIcons: true
        });

        // 添加样式类
        if (this.addTestCaseButton && this.addTestCaseButton.element) {
            this.addTestCaseButton.element.classList.add('test-case-add-button');

            // 使用DOM API创建元素，而不是直接设置innerHTML
            const iconSpan = document.createElement('span');
            iconSpan.className = 'codicon codicon-add';
            this.addTestCaseButton.element.textContent = '';
            this.addTestCaseButton.element.appendChild(iconSpan);
        }

        // 添加点击事件
        this.addTestCaseButton.onDidClick(async () => {
            try {
                await this.handleAddTestCase();
            } catch (error) {
                this.logService.error('处理新增测试用例按钮点击时出错:', error);
                this.notificationService.error(localize('addTestCaseError', '新增测试用例时出错: {0}', error.message));
            }
        });
    }

    /**
     * 处理新增测试用例按钮点击
     */
    private async handleAddTestCase(): Promise<void> {
        // 检查是否选择了测试集
        if (!this.currentTestSetName) {
            this.notificationService.info(localize('selectTestSetFirst', '请先选择一个测试集'));
            return;
        }

        // 创建新增测试用例对话框
        this.createAddTestCaseWebview(this.currentTestSetName);
    }

    /**
     * 创建新增测试用例对话框
     */
    private async createAddTestCaseWebview(testSetName: string): Promise<void> {
        // 直接在代码中定义HTML模板
        const html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
    <title>新增测试用例</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        h1 {
            color: var(--vscode-editor-foreground);
            font-size: 1.5em;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--vscode-panel-border);
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: var(--vscode-input-foreground);
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 2px;
        }
        input:focus, select:focus {
            outline: 1px solid var(--vscode-focusBorder);
        }
        .button-container {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        button {
            padding: 8px 16px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 2px;
            cursor: pointer;
            margin-left: 10px;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        button.secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }
        button.secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background-color: var(--vscode-button-background);
        }
        .error {
            color: var(--vscode-errorForeground);
            font-size: 0.9em;
            margin-top: 5px;
            display: none;
        }
        .warning {
            color: var(--vscode-editorWarning-foreground);
            font-size: 0.9em;
            margin-top: 5px;
            display: none;
        }
        .status-update {
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 8px 12px;
            background-color: var(--vscode-notificationToast-background, rgba(50, 50, 50, 0.95));
            color: var(--vscode-notificationToast-foreground, #ffffff);
            border-radius: 4px;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            animation: fadeIn 0.3s ease-out;
            font-weight: 500;
            border: 1px solid var(--vscode-notificationToast-border, rgba(80, 80, 80, 0.5));
        }
        .perf-info {
            position: fixed;
            bottom: 10px;
            right: 10px;
            padding: 8px 12px;
            background-color: var(--vscode-editorHint-background, #f39c12);
            color: var(--vscode-editorHint-foreground, white);
            border-radius: 4px;
            z-index: 1000;
            font-size: 0.9em;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            animation: fadeIn 0.3s ease-out;
        }
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            display: none;
        }
        .dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: var(--vscode-editor-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 20px;
            min-width: 400px;
            max-width: 500px;
            z-index: 2001;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
        }
        .dialog h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: var(--vscode-editorWarning-foreground);
        }
        .dialog-content {
            margin-bottom: 20px;
            line-height: 1.4;
        }
        .dialog-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 手动编辑用例步骤的样式 */
        .steps-section {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            background-color: var(--vscode-editor-background);
            display: none; /* 默认隐藏 */
        }

        .steps-section.show {
            display: block;
        }

        /* 自定义参数样式 */
        .custom-params-section {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            background-color: var(--vscode-editor-background);
        }

        .custom-params-section h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: var(--vscode-foreground);
            font-size: 1.1em;
        }

        .custom-params-container {
            margin-bottom: 15px;
        }

        .custom-param-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            background-color: var(--vscode-input-background);
        }

        .custom-param-input {
            flex: 1;
            padding: 6px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 2px;
            margin-right: 8px;
        }

        .custom-param-input:focus {
            outline: 1px solid var(--vscode-focusBorder);
        }

        .custom-param-type-select {
            width: 100px;
            padding: 6px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 2px;
            margin-right: 8px;
        }

        .custom-param-remove-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: var(--vscode-errorForeground);
            color: white;
            font-size: 0.8em;
        }

        .custom-param-remove-btn:hover {
            background-color: #d73a49;
        }

        .add-custom-param-btn {
            padding: 8px 16px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .add-custom-param-btn:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .steps-section h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: var(--vscode-foreground);
            font-size: 1.1em;
        }

        .steps-container {
            margin-bottom: 15px;
        }

        .step-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            background-color: var(--vscode-input-background);
        }

        .step-number {
            min-width: 30px;
            padding: 5px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border-radius: 3px;
            text-align: center;
            margin-right: 10px;
            font-size: 0.9em;
        }

        .step-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .step-input-group {
            display: flex;
            flex-direction: column;
        }

        .step-input-group label {
            font-size: 0.9em;
            margin-bottom: 3px;
            color: var(--vscode-foreground);
        }

        .step-input-group input, .step-input-group textarea {
            padding: 6px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 2px;
            font-family: inherit;
            font-size: 0.9em;
        }

        .step-input-group textarea {
            resize: vertical;
            min-height: 60px;
        }

        .step-actions {
            margin-left: 10px;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .step-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8em;
            min-width: 60px;
        }

        .step-btn.delete {
            background-color: var(--vscode-errorForeground);
            color: white;
        }

        .step-btn.delete:hover {
            background-color: #d73a49;
        }

        .add-step-btn {
            padding: 8px 16px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }

        .add-step-btn:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .steps-hint {
            color: var(--vscode-descriptionForeground);
            font-size: 0.9em;
            margin-bottom: 10px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>新增测试用例</h1>
        <form id="testCaseForm">
            <div class="form-group">
                <label for="id">测试用例ID</label>
                <div style="display: flex;">
                    <input type="number" id="id" name="id" required>
                </div>
                <div class="error" id="idExistsError" style="display:none">ID已存在</div>
                <div class="warning" id="idConflictWarning" style="display:none">测试用例ID已存在，如需覆盖请确认</div>
                <div class="error" id="idError">测试用例ID不能为空</div>
            </div>
            <div class="form-group">
                <label for="name">测试用例名称</label>
                <input type="text" id="name" name="name" required>
                <div class="error" id="nameError">测试用例名称不能为空</div>
            </div>
            <div class="form-group">
                <label for="module">测试用例模块</label>
                <input type="text" id="module" name="module" required>
                <div class="error" id="moduleError">测试用例模块不能为空</div>
            </div>
            <div class="form-group">
                <label for="recordType">录制类型</label>
                <select id="recordType" name="recordType" required>
                    <option value="UNI">UNI</option>
                    <option value="OCR">OCR</option>
                </select>
            </div>
            <div class="form-group">
                <label for="directRecord">是否直接录制</label>
                <select id="directRecord" name="directRecord">
                    <option value="true">是</option>
                    <option value="false">否</option>
                </select>
            </div>
            <div class="form-group">
                <label for="manualVerification">是否人工校验</label>
                <select id="manualVerification" name="manualVerification">
                    <option value="false">否</option>
                    <option value="true">是</option>
                </select>
            </div>
            <input type="hidden" id="testSetName" name="testSetName" value="${testSetName}">

            <!-- 自定义参数区域 -->
            <div class="custom-params-section" id="customParamsSection">
                <h3>⚙️ 自定义用例参数</h3>
                <div class="steps-hint">
                    您可以为测试用例添加自定义参数，这些参数将出现在生成的用例头部：
                </div>
                <div class="custom-params-container" id="customParamsContainer">
                    <!-- 自定义参数项将动态添加到这里 -->
                </div>
                <button type="button" class="add-custom-param-btn" id="addCustomParamBtn">+ 添加自定义参数</button>
            </div>

            <!-- 手动编辑用例步骤区域 -->
            <div class="steps-section" id="stepsSection">
                <h3>📝 手动编辑用例步骤</h3>
                <div class="steps-hint">
                    无法从禅道获取用例步骤信息，您可以手动添加测试步骤（可选，也可以不添加步骤直接创建用例）：
                </div>
                <div class="steps-container" id="stepsContainer">
                    <!-- 步骤项将动态添加到这里 -->
                </div>
                <button type="button" class="add-step-btn" id="addStepBtn">+ 添加步骤</button>
            </div>

            <div class="button-container">
                <button type="button" class="secondary" id="cancelButton">取消</button>
                <button type="submit" id="submitButton" disabled>确定</button>
            </div>
        </form>
    </div>

    <!-- 覆盖确认对话框 -->
    <div class="overlay" id="confirmOverlay">
        <div class="dialog">
            <h3>⚠️ 测试用例ID冲突</h3>
            <div class="dialog-content">
                <p>测试用例ID "<span id="conflictId"></span>" 已经存在。</p>
                <p>是否要覆盖现有的测试用例文件？</p>
                <p><strong>注意：</strong>覆盖操作将永久删除原有文件内容，此操作不可恢复。</p>
            </div>
            <div class="dialog-buttons">
                <button type="button" class="secondary" id="cancelOverwriteBtn">取消</button>
                <button type="button" id="confirmOverwriteBtn">确认覆盖</button>
            </div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let currentSteps = []; // Variable to store steps (改为数组)
        let idExistsState = false; // 标记ID是否存在
        let userConfirmedOverwrite = false; // 标记用户是否确认覆盖
        let manualStepsMode = false; // 标记是否为手动步骤模式
        let customParameters = []; // 存储自定义参数

        document.addEventListener('DOMContentLoaded', () => {
            const form = document.getElementById('testCaseForm');
            const submitButton = document.getElementById('submitButton');
            const cancelButton = document.getElementById('cancelButton'); // 获取取消按钮元素

            // 确定按钮点击事件
            submitButton.addEventListener('click', () => {
                const idInput = document.getElementById('id');
                const idVal = idInput.value.trim();

                if (idExistsState && !userConfirmedOverwrite) {
                    // 如果ID已存在且用户未确认覆盖，显示覆盖确认对话框
                    showOverwriteConfirmDialog();
                    return;
                }

                submitForm();
            });

            // 取消按钮点击事件
            cancelButton.addEventListener('click', () => {
                // 发送关闭WebView的消息
                vscode.postMessage({ command: 'cancel' });
            });

            const idInput = document.getElementById('id');

            // 当用户开始输入新ID时，立即清空之前的缓存信息
            idInput.addEventListener('input', () => {
                // 清空之前缓存的用例信息
                currentSteps = [];
                // 重置状态标记
                userConfirmedOverwrite = false;
                idExistsState = false;

                // 隐藏手动编辑区域
                hideStepsSection();

                // 隐藏之前的警告信息
                const idExistsError = document.getElementById('idExistsError');
                const idConflictWarning = document.getElementById('idConflictWarning');
                if (idExistsError) idExistsError.style.display = 'none';
                if (idConflictWarning) idConflictWarning.style.display = 'none';

                updateSubmitButtonState();
            });

            idInput.addEventListener('blur', () => {
                const idVal = idInput.value.trim();
                if (idVal) {
                    // 重置状态
                    userConfirmedOverwrite = false;

                    // 立即清空之前缓存的用例信息，防止新ID获取失败时显示旧信息
                    currentSteps = [];

                    // 隐藏手动编辑区域，等待新的结果
                    hideStepsSection();

                    statusUpdate('正在检查ID是否存在...');
                    vscode.postMessage({ command: 'checkId', data: idVal });

                    // 然后获取用例详情
                    statusUpdate('正在获取用例 ' + idVal + ' 的详情...');
                    vscode.postMessage({ command: 'getCaseDetail', data: idVal });
                }
            });

            // 覆盖确认对话框事件
            const confirmOverlay = document.getElementById('confirmOverlay');
            const cancelOverwriteBtn = document.getElementById('cancelOverwriteBtn');
            const confirmOverwriteBtn = document.getElementById('confirmOverwriteBtn');

            cancelOverwriteBtn.addEventListener('click', () => {
                hideOverwriteConfirmDialog();
                userConfirmedOverwrite = false;
            });

            confirmOverwriteBtn.addEventListener('click', () => {
                hideOverwriteConfirmDialog();
                userConfirmedOverwrite = true;
                submitForm(); // 用户确认后直接提交
            });

            // 点击遮罩层关闭对话框
            confirmOverlay.addEventListener('click', (e) => {
                if (e.target === confirmOverlay) {
                    hideOverwriteConfirmDialog();
                    userConfirmedOverwrite = false;
                }
            });

            // 添加步骤按钮事件监听
            const addStepBtn = document.getElementById('addStepBtn');
            addStepBtn.addEventListener('click', () => {
                addNewStep();
            });

            // 添加自定义参数按钮事件监听
            const addCustomParamBtn = document.getElementById('addCustomParamBtn');
            addCustomParamBtn.addEventListener('click', () => {
                addNewCustomParameter();
            });
        });

        // 步骤管理函数
        function showStepsSection() {
            const stepsSection = document.getElementById('stepsSection');
            stepsSection.classList.add('show');
            manualStepsMode = true;

            // 总是调用renderSteps，它会处理空步骤的情况
            renderSteps();
        }

        function hideStepsSection() {
            const stepsSection = document.getElementById('stepsSection');
            stepsSection.classList.remove('show');
            manualStepsMode = false;
        }

        function addNewStep() {
            const newStep = {
                index: currentSteps.length + 1,
                desc: '',
                expect: ''
            };
            currentSteps.push(newStep);
            renderSteps();
        }

        function removeStep(stepIndex) {
            currentSteps.splice(stepIndex, 1);
            // 重新编号
            currentSteps.forEach((step, index) => {
                step.index = index + 1;
            });
            renderSteps();
        }

        function updateStep(stepIndex, field, value) {
            if (currentSteps[stepIndex]) {
                currentSteps[stepIndex][field] = value;
            }
        }

        function renderSteps() {
            const container = document.getElementById('stepsContainer');
            // 使用安全的方式清空容器
            while (container.firstChild) {
                container.removeChild(container.firstChild);
            }

            if (currentSteps.length === 0) {
                // 显示友好提示
                const emptyHint = document.createElement('div');
                emptyHint.style.cssText = 'padding: 20px; text-align: center; color: var(--vscode-descriptionForeground); font-style: italic;';
                emptyHint.textContent = '暂无步骤，您可以点击下面的"添加步骤"按钮来添加，或直接创建用例（无步骤）';
                container.appendChild(emptyHint);
                return;
            }

            currentSteps.forEach((step, index) => {
                const stepElement = createStepElement(step, index);
                container.appendChild(stepElement);
            });
        }

        function createStepElement(step, index) {
            const stepDiv = document.createElement('div');
            stepDiv.className = 'step-item';

            // 创建步骤编号
            const stepNumber = document.createElement('div');
            stepNumber.className = 'step-number';
            stepNumber.textContent = step.index;

            // 创建步骤内容容器
            const stepContent = document.createElement('div');
            stepContent.className = 'step-content';

            // 创建步骤描述组
            const descGroup = document.createElement('div');
            descGroup.className = 'step-input-group';

            const descLabel = document.createElement('label');
            descLabel.textContent = '步骤描述\uff1a';

            const descTextarea = document.createElement('textarea');
            descTextarea.placeholder = '请输入测试步骤的具体操作...';
            descTextarea.value = step.desc;
            descTextarea.onchange = function() { updateStep(index, 'desc', this.value); };

            descGroup.appendChild(descLabel);
            descGroup.appendChild(descTextarea);

            // 创建期望结果组
            const expectGroup = document.createElement('div');
            expectGroup.className = 'step-input-group';

            const expectLabel = document.createElement('label');
            expectLabel.textContent = '期望结果：';

            const expectTextarea = document.createElement('textarea');
            expectTextarea.placeholder = '请输入期望的测试结果...';
            expectTextarea.value = step.expect;
            expectTextarea.onchange = function() { updateStep(index, 'expect', this.value); };

            expectGroup.appendChild(expectLabel);
            expectGroup.appendChild(expectTextarea);

            stepContent.appendChild(descGroup);
            stepContent.appendChild(expectGroup);

            // 创建操作按钮容器
            const stepActions = document.createElement('div');
            stepActions.className = 'step-actions';

            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'step-btn delete';
            deleteBtn.textContent = '删除';
            deleteBtn.onclick = function() { removeStep(index); };

            stepActions.appendChild(deleteBtn);

            // 组装所有元素
            stepDiv.appendChild(stepNumber);
            stepDiv.appendChild(stepContent);
            stepDiv.appendChild(stepActions);

            return stepDiv;
        }

        // 自定义参数管理函数
        function addNewCustomParameter() {
            const newParam = {
                key: '',
                value: '',
                type: 'string'
            };
            customParameters.push(newParam);
            renderCustomParameters();
        }

        function removeCustomParameter(paramIndex) {
            customParameters.splice(paramIndex, 1);
            renderCustomParameters();
        }

        function updateCustomParameter(paramIndex, field, value) {
            if (customParameters[paramIndex]) {
                customParameters[paramIndex][field] = value;
            }
        }

        function renderCustomParameters() {
            const container = document.getElementById('customParamsContainer');
            container.innerHTML = '';

            customParameters.forEach((param, index) => {
                const paramElement = createCustomParameterElement(param, index);
                container.appendChild(paramElement);
            });
        }

        function createCustomParameterElement(param, index) {
            const paramDiv = document.createElement('div');
            paramDiv.className = 'custom-param-item';

            // 创建参数名称输入框
            const keyInput = document.createElement('input');
            keyInput.type = 'text';
            keyInput.className = 'custom-param-input';
            keyInput.placeholder = '参数名称';
            keyInput.value = param.key;
            keyInput.onchange = function() { updateCustomParameter(index, 'key', this.value); };

            // 创建参数值输入框
            const valueInput = document.createElement('input');
            valueInput.type = 'text';
            valueInput.className = 'custom-param-input';
            valueInput.placeholder = '参数值';
            valueInput.value = param.value;
            valueInput.onchange = function() { updateCustomParameter(index, 'value', this.value); };

            // 创建类型选择框
            const typeSelect = document.createElement('select');
            typeSelect.className = 'custom-param-type-select';
            typeSelect.onchange = function() { updateCustomParameter(index, 'type', this.value); };

            const types = [
                { value: 'string', text: '字符串' },
                { value: 'int', text: '整数' },
                { value: 'bool', text: '布尔值' },
                { value: 'list', text: '列表' },
                { value: 'dict', text: '字典' }
            ];

            types.forEach(type => {
                const option = document.createElement('option');
                option.value = type.value;
                option.textContent = type.text;
                if (param.type === type.value) {
                    option.selected = true;
                }
                typeSelect.appendChild(option);
            });

            // 创建删除按钮
            const removeBtn = document.createElement('button');
            removeBtn.type = 'button';
            removeBtn.className = 'custom-param-remove-btn';
            removeBtn.textContent = '删除';
            removeBtn.onclick = function() { removeCustomParameter(index); };

            // 添加所有元素到容器
            paramDiv.appendChild(keyInput);
            paramDiv.appendChild(valueInput);
            paramDiv.appendChild(typeSelect);
            paramDiv.appendChild(removeBtn);

            return paramDiv;
        }

        function showOverwriteConfirmDialog() {
            const idInput = document.getElementById('id');
            const conflictIdSpan = document.getElementById('conflictId');
            const overlay = document.getElementById('confirmOverlay');

            conflictIdSpan.textContent = idInput.value;
            overlay.style.display = 'block';
        }

        function hideOverwriteConfirmDialog() {
            const overlay = document.getElementById('confirmOverlay');
            overlay.style.display = 'none';
        }

        function statusUpdate(text, type = 'info') {
            const statusArea = document.createElement('div');
            statusArea.className = 'status-update';
            if (type === 'error') {
                statusArea.style.backgroundColor = 'var(--vscode-errorBackground, #f14c4c)';
                statusArea.style.borderColor = 'var(--vscode-errorBorder, #e51400)';
            }
            statusArea.textContent = text;
            document.body.appendChild(statusArea);
            setTimeout(() => statusArea.remove(), type === 'error' ? 5000 : 3000);
        }

        function submitForm() {
            try {
                console.log('🚀 开始submitForm函数');

                // 基本表单验证
                const idValue = document.getElementById('id').value.trim();
                const nameValue = document.getElementById('name').value.trim();
                const moduleValue = document.getElementById('module').value.trim();

                console.log('📋 基本字段验证:', { id: idValue, name: nameValue, module: moduleValue });

                if (!idValue || !nameValue || !moduleValue) {
                    console.error('❌ 基本字段验证失败');
                    statusUpdate('请填写完整的基本信息', 'error');
                    return;
                }

                // 注释：不再强制要求步骤内容，允许创建没有步骤的测试用例
                // 这样当禅道获取失败且用户不想手动添加步骤时，也能正常创建用例

                // 如果在手动模式下且用户添加了步骤，验证每个步骤是否都已填写完整
                if (manualStepsMode && currentSteps.length > 0) {
                    console.log('🔍 验证手动步骤:', currentSteps);

                    // 检查是否有任何有效的步骤内容
                    const hasValidSteps = currentSteps.some(step =>
                        step.desc && step.desc.trim() && step.expect && step.expect.trim()
                    );

                    // 如果有步骤但是都是空的，清空步骤数组（用户不想添加步骤）
                    if (!hasValidSteps) {
                        console.log('⚠️ 用户在手动模式下没有填写任何有效步骤，将清空步骤数组');
                        currentSteps = [];
                    } else {
                        // 如果有有效步骤，则验证所有步骤都填写完整
                        for (let i = 0; i < currentSteps.length; i++) {
                            const step = currentSteps[i];
                            if (!step.desc.trim() || !step.expect.trim()) {
                                console.error('❌ 步骤验证失败:', step);
                                statusUpdate('步骤' + step.index + '的描述或期望结果不能为空', 'error');
                                return;
                            }
                        }
                    }
                }

                // 验证自定义参数
                const validCustomParameters = [];
                if (customParameters && customParameters.length > 0) {
                    console.log('🔍 验证自定义参数:', customParameters);
                    for (let i = 0; i < customParameters.length; i++) {
                        const param = customParameters[i];
                        if (param.key && param.key.trim()) {
                            validCustomParameters.push({
                                key: param.key.trim(),
                                value: param.value || '',
                                type: param.type || 'string'
                            });
                        }
                    }
                }

                const formData = {
                    id: idValue,
                    name: nameValue,
                    module: moduleValue,
                    recordType: document.getElementById('recordType').value,
                    directRecord: document.getElementById('directRecord').value === 'true',
                    manualVerification: document.getElementById('manualVerification').value === 'true',
                    testSetName: document.getElementById('testSetName').value,
                    steps: currentSteps, // Include stored steps (should be an array now)
                    customParameters: validCustomParameters, // 包含验证过的自定义参数
                    overwrite: userConfirmedOverwrite, // 添加覆盖标记
                    manualStepsMode: manualStepsMode // 标记是否为手动模式
                };

                console.log('📤 准备发送表单数据:', formData);
                statusUpdate('正在创建测试用例...', 'info');

                // 检查vscode对象是否存在
                if (!vscode) {
                    console.error('❌ vscode对象不存在！');
                    statusUpdate('WebView通信错误：vscode对象不存在', 'error');
                    return;
                }

                // 发送消息
                try {
                    console.log('📡 发送WebView消息...');
                    vscode.postMessage({ command: 'createTestCase', data: formData });
                    console.log('✅ WebView消息发送成功');
                } catch (msgError) {
                    console.error('❌ 发送WebView消息失败:', msgError);
                    statusUpdate('发送消息失败: ' + msgError.message, 'error');
                    return;
                }

            } catch (error) {
                console.error('❌ submitForm函数出错:', error);
                statusUpdate('提交表单时出错: ' + error.message, 'error');
            }
        }

        function updateSubmitButtonState() {
            const submitBtn = document.getElementById('submitButton');
            const idInput = document.getElementById('id');
            const nameInput = document.getElementById('name');
            const moduleInput = document.getElementById('module');

            // 基本表单验证 - 只检查必填字段是否已填写
            const formValid = idInput.value.trim() && nameInput.value.trim() && moduleInput.value.trim();

            // 只要表单有效，按钮就应该可点击，不再依赖API调用结果
            if (!formValid) {
                submitBtn.disabled = true;
                return;
            }

            // 如果ID存在冲突，显示覆盖确认文本
            if (idExistsState && !userConfirmedOverwrite) {
                submitBtn.disabled = false; // 允许点击以显示确认对话框
                submitBtn.textContent = '确认覆盖';
            } else {
                submitBtn.disabled = false;
                submitBtn.textContent = '确定';
            }
        }

        // 页面加载后立即检查表单状态，确保按钮状态正确
        document.addEventListener('DOMContentLoaded', () => {
            // 初始化时检查按钮状态
            updateSubmitButtonState();

            // 为所有输入字段添加事件监听，实时更新按钮状态
            const formInputs = document.querySelectorAll('input, select');
            formInputs.forEach(input => {
                input.addEventListener('input', updateSubmitButtonState);
                input.addEventListener('change', updateSubmitButtonState);
            });
        });

        // 监听后端返回的消息
        window.addEventListener('message', event => {
            const msg = event.data;
            console.log('🔄 WebView收到消息:', msg);

            if (msg.command === 'checkIdResult') {
                const exists = msg.data.exists;
                idExistsState = exists;

                const idExistsError = document.getElementById('idExistsError');
                const idConflictWarning = document.getElementById('idConflictWarning');

                if (exists) {
                    idExistsError.style.display = 'none';
                    idConflictWarning.style.display = 'block';
                    statusUpdate('检测到ID冲突，需要确认是否覆盖');
                } else {
                    idExistsError.style.display = 'none';
                    idConflictWarning.style.display = 'none';
                    statusUpdate('ID可用');
                }

                updateSubmitButtonState();
            } else if (msg.command === 'caseDetail') {
                const d = msg.data;
                const nameEl = document.getElementById('name');
                if (nameEl) nameEl.value = d.title || '';
                const moduleEl = document.getElementById('module');
                if (moduleEl) moduleEl.value = d.module || '';
                const recEl = document.getElementById('recordType');
                if (recEl && d.recordType) recEl.value = d.recordType.toLowerCase();

                // Store steps array (or empty array if not present/valid)
                currentSteps = Array.isArray(d.steps) ? d.steps : [];

                // 成功获取到用例信息，隐藏手动编辑区域
                hideStepsSection();

                updateSubmitButtonState();
                statusUpdate('用例详情已自动填充');
            } else if (msg.command === 'error') {
                // 显示错误提示并允许用户手动填写
                console.error('收到错误消息:', msg);
                statusUpdate('获取用例详情失败: ' + msg.text, 'error');

                // 重置idExistsState，确保用户可以手动填写并提交表单
                idExistsState = false;

                // 清空之前缓存的用例信息，防止显示旧的用例步骤
                currentSteps = [];

                // 清空表单字段（除了ID字段）
                const nameEl = document.getElementById('name');
                const moduleEl = document.getElementById('module');
                if (nameEl) nameEl.value = '';
                if (moduleEl) moduleEl.value = '';

                // 隐藏错误提示
                const idExistsError = document.getElementById('idExistsError');
                const idConflictWarning = document.getElementById('idConflictWarning');
                if (idExistsError) idExistsError.style.display = 'none';
                if (idConflictWarning) idConflictWarning.style.display = 'none';

                // 显示手动编辑步骤区域
                showStepsSection();
                statusUpdate('禅道获取失败，您可以手动填写用例信息。注意：可以不添加步骤，后续通过录制功能补充。', 'info');

                // 更新按钮状态为可点击
                updateSubmitButtonState();
            } else if (msg.command === 'createSuccess') {
                // 处理创建成功的消息
                console.log('测试用例创建成功:', msg);
                statusUpdate('测试用例创建成功！', 'info');
                // 可以选择关闭窗口或者重置表单
                setTimeout(() => {
                    window.close();
                }, 1000);
            } else if (msg.command === 'createFailed') {
                // 处理创建失败的消息
                console.error('测试用例创建失败:', msg);
                statusUpdate('创建测试用例失败: ' + (msg.text || msg.error || '未知错误'), 'error');
            } else {
                console.warn('收到未知消息:', msg);
            }
        });
    </script>
</body>
</html>`;

        // 创建WebView
        const webviewPanel = this.webviewWorkbenchService.openWebview({
            title: localize('addTestCase', '新建测试用例'),
            options: {
                enableFindWidget: false,
                retainContextWhenHidden: true
            },
            contentOptions: {
                allowScripts: true,
                localResourceRoots: []
            },
            extension: undefined
        }, 'testCaseWebview', localize('addTestCase', '新建测试用例'), {});

        // 设置WebView内容
        const webview = webviewPanel.webview;

        // 为了避免可能由于ES模块导入导致的语法错误，确保内容正确设置
        try {
            // 使用标准方法设置HTML内容
            webview.setHtml(html);
            this.logService.info(`WebView内容已设置`);
        } catch (e) {
            this.logService.error(`设置WebView内容失败: ${e instanceof Error ? e.message : String(e)}`);
        }

        // 处理WebView消息
        webview.onMessage(async (message: any) => {
            this.logService.info(`🔄 收到WebView消息: ${JSON.stringify(message)}`);
            console.log('🔄 收到WebView消息:', message);

            // 处理消息可能被包装在message字段中的情况
            const actualMessage = message.message || message;
            this.logService.info(`🔧 处理消息: ${JSON.stringify(actualMessage)}`);
            console.log('🔧 处理消息:', actualMessage);

            if (actualMessage.command === 'cancel') {
                // 处理取消按钮点击
                this.logService.info('用户取消创建测试用例');
                webviewPanel.dispose();
                return;
            } else if (actualMessage.command === 'createTestCase') {
                this.logService.info('🚀 处理创建测试用例命令');
                console.log('🚀 收到createTestCase命令:', actualMessage);
                try {
                    const testCaseData: TestCaseData = {
                        id: actualMessage.data.id,
                        name: actualMessage.data.name,
                        module: actualMessage.data.module,
                        recordType: actualMessage.data.recordType || 'record', // 设置默认值，避免未填写时出错
                        directRecord: actualMessage.data.directRecord || false, // 设置默认值
                        manualVerification: actualMessage.data.manualVerification || false, // 设置默认值
                        driver: actualMessage.data.driver || '',  // 默认为空字符串
                        testSetName,
                        steps: actualMessage.data.steps || [], // 确保steps始终为数组
                        customParameters: actualMessage.data.customParameters || [], // 自定义参数
                    };

                    this.logService.info(`📋 测试用例数据: ${JSON.stringify(testCaseData, null, 2)}`);
                    console.log('📋 测试用例数据:', testCaseData);

                    // 特别记录步骤信息
                    const stepsArray = testCaseData.steps || [];
                    this.logService.info(`📝 步骤数量: ${stepsArray.length}`);
                    if (stepsArray.length === 0) {
                        this.logService.info('⚠️ 注意：创建的是没有步骤的测试用例');
                        console.log('⚠️ 注意：创建的是没有步骤的测试用例');
                    }

                    // 检查是否需要覆盖确认
                    const shouldOverwrite = actualMessage.data.overwrite;

                    // 如果没有明确的覆盖确认，再次检查文件是否存在
                    if (!shouldOverwrite) {
                        const testcaseUri = await this.pathResolver.getTestcasePath();
                        if (testcaseUri) {
                            const fileUri = joinPath(testcaseUri, testSetName, `${testCaseData.id}.yml`);
                            try {
                                const exists = await this.fileService.exists(fileUri);
                                if (exists) {
                                    // 文件存在但用户没有确认覆盖，发送错误消息
                                    this.logService.warn(`测试用例文件已存在且用户未确认覆盖: ${testCaseData.id}`);
                                    webview.postMessage({
                                        command: 'error',
                                        text: '测试用例文件已存在，需要确认覆盖'
                                    });
                                    return;
                                }
                            } catch (error) {
                                this.logService.warn(`检查文件是否存在时出错: ${error}`);
                            }
                        }
                    } else {
                        this.logService.info(`用户已确认覆盖现有测试用例: ${testCaseData.id}`);
                    }

                    // 创建测试用例文件并获取文件路径
                    this.logService.info(`开始创建测试用例文件: ${testCaseData.id}`);
                    const testCaseFilePath = await this.createTestCaseFile(testSetName, testCaseData);
                    this.logService.info(`测试用例文件创建成功: ${testCaseData.id}，路径: ${testCaseFilePath.toString()}`);

                    const successMessage = shouldOverwrite
                        ? localize('testCaseOverwritten', '测试用例 {0} 覆盖成功！', testCaseData.id)
                        : localize('testCaseCreated', '测试用例 {0} 创建成功！', testCaseData.id);

                    this.notificationService.info(successMessage);

                    // 向WebView发送成功消息
                    webview.postMessage({
                        command: 'createSuccess',
                        text: successMessage,
                        data: { id: testCaseData.id, name: testCaseData.name }
                    });

                    // 关闭WebView
                    this.logService.info('关闭测试用例创建窗口');
                    webviewPanel.dispose();

                    // 刷新测试用例列表
                    this.logService.info(`刷新测试用例列表: ${testSetName}`);
                    this.refreshTestCaseList(testSetName);

                    // 如果选择了直接录制，启动录制功能
                    if (testCaseData.directRecord) {
                        this.logService.info(`启动录制功能: ${testCaseData.id}`);

                        let appListForRecording: string[] = [];
                        // 假设应用对象信息在 actualMessage.data.driver 中
                        if (actualMessage.data.driver && typeof actualMessage.data.driver === 'string') {
                            appListForRecording.push(actualMessage.data.driver);
                        }
                        // 你也可以检查一个名为 appList 的字段，如果前端是以此命名的
                        // else if (actualMessage.data.appList && Array.isArray(actualMessage.data.appList)) {
                        //     appListForRecording = actualMessage.data.appList.filter(app => typeof app === 'string');
                        // }

                        // 如果 appListForRecording 为空，则从当前测试集配置文件读取默认 AppName
                        if (appListForRecording.length === 0 && this.currentTestSetPath) {
                            try {
                                const { fileService, logService } = this;
                                // 配置文件路径：testSetName_conf.json
                                let configUri: URI;
                                const pathStr = this.currentTestSetPath.toString();
                                if (pathStr.toLowerCase().endsWith('.json')) {
                                    // currentPath 本身是配置文件
                                    configUri = this.currentTestSetPath;
                                } else {
                                    // currentPath 是目录，配置文件位于父目录
                                    configUri = joinPath(dirname(this.currentTestSetPath), `${testSetName}_conf.json`);
                                }
                                const content = await fileService.readFile(configUri);
                                const configJson = JSON.parse(content.value.toString());
                                const suiteConfig = configJson[testSetName] || {};
                                const defaultApp = suiteConfig.AppName;
                                if (defaultApp && typeof defaultApp === 'string') {
                                    appListForRecording.push(defaultApp);
                                    logService.info(`使用测试集配置的默认应用对象: ${defaultApp}`);
                                }
                            } catch (err) {
                                this.logService.warn(`读取测试集默认应用对象失败: ${err}`);

                                // OCR fallback 逻辑
                                if (appListForRecording.length === 0) {
                                    const featureField = (actualMessage.data as any).feature || (testCaseData as any).feature;
                                    const driverField = actualMessage.data.driver || (testCaseData as any).driver;

                                    const inferredApp = (typeof featureField === "string" && featureField.trim())
                                        ? featureField.trim()
                                        : (typeof driverField === "string" && driverField.trim() ? driverField.trim() : undefined);

                                    if (inferredApp) {
                                        appListForRecording.push(inferredApp);
                                        this.logService.info(`WebView: 推断应用对象为: ${inferredApp}`);
                                    } else if ((testCaseData.recordType && String(testCaseData.recordType).toUpperCase() === "OCR")) {
                                        // 对于OCR类型，使用当前测试集名称
                                        if (testSetName && testSetName.trim() !== "") {
                                            appListForRecording.push(testSetName.trim());
                                            this.logService.info(`WebView: OCR 场景 fallback 使用测试集名称作为应用对象: ${testSetName}`);
                                        }
                                    }
                                }
                            }
                        }

                        // 传入完整上下文，包括用例步骤、应用列表和文件路径
                        this.startRecording({
                            id: testCaseData.id,
                            name: testCaseData.name,
                            TestCaseSteps: testCaseData.steps || [],
                            TestCaseAppList: appListForRecording, // 新增应用列表
                            ymlPath: testCaseFilePath.toString() // 新增文件路径
                        });
                        this.logService.info(`启动录制功能，传递文件路径: ${testCaseFilePath.toString()}`);
                    }
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : String(error);
                    this.logService.error(`创建测试用例失败: ${errorMessage}`, error);

                    // 向WebView发送失败消息
                    webview.postMessage({
                        command: 'createFailed',
                        text: errorMessage,
                        error: errorMessage,
                        detail: error instanceof Error ? error.stack : undefined
                    });

                    // 同时发送error命令以兼容现有逻辑
                    webview.postMessage({
                        command: 'error',
                        text: localize('testCaseCreateFailed', '创建测试用例失败: {0}', errorMessage)
                    } as any);
                }
            } else if (actualMessage.command === 'cancel') {
                this.logService.info('处理取消命令，关闭测试用例创建窗口');
                // 关闭WebView
                webviewPanel.dispose();
            } else if (actualMessage.command === 'checkId') {
                const id: string = actualMessage.data;
                this.logService.info(`检查测试用例ID是否存在: ${id}`);

                try {
                    let exists = false;

                    // 优先使用当前测试集的实际路径
                    if (this.currentTestSetPath) {
                        const pathStr = this.currentTestSetPath.toString();
                        const isJsonFile = pathStr.toLowerCase().endsWith('.json') || pathStr.toLowerCase().endsWith('_conf.json');

                        let testcaseDirPath: URI;
                        if (isJsonFile) {
                            // 如果是配置文件，使用其所在的目录
                            testcaseDirPath = dirname(this.currentTestSetPath);
                        } else {
                            // 如果不是配置文件，直接使用该路径
                            testcaseDirPath = this.currentTestSetPath;
                        }

                        const fileUri = joinPath(testcaseDirPath, `${id}.yml`);
                        try {
                            exists = await this.fileService.exists(fileUri);
                            this.logService.info(`使用当前测试集路径检查文件: ${fileUri.toString()}, 存在: ${exists}`);
                        } catch (error) {
                            this.logService.warn(`检查文件存在性时出错: ${error}`);
                        }
                    } else {
                        // 如果没有当前测试集路径，使用路径解析器获取测试用例路径
                        const testcaseUri = await this.pathResolver.getTestcasePath();
                        if (testcaseUri) {
                            const fileUri = joinPath(testcaseUri, testSetName, `${id}.yml`);
                            try {
                                exists = await this.fileService.exists(fileUri);
                                this.logService.info(`使用路径解析器检查文件: ${fileUri.toString()}, 存在: ${exists}`);
                            } catch (error) {
                                this.logService.warn(`检查文件存在性时出错: ${error}`);
                            }
                        }
                    }

                    webview.postMessage({
                        command: 'checkIdResult',
                        data: { exists }
                    });
                } catch (error) {
                    this.logService.error(`ID校验出错: ${error}`);
                    webview.postMessage({
                        command: 'checkIdResult',
                        data: { exists: false }
                    });
                }
            } else if (actualMessage.command === 'getCaseDetail') {
                const caseId = actualMessage.data;
                this.logService.info(`通过ZenTaoService获取用例详情: ${caseId}`);

                try {
                    // 显示加载信息给用户
                    webview.postMessage({ command: 'statusUpdate', text: `正在获取用例 ${caseId} 的详情...` });

                    // 调用ZenTaoService服务，这是IPC通信的关键部分
                    const startTime = performance.now();
                    const detail = await this.zenTaoService.getCaseDetail(caseId);
                    const endTime = performance.now();

                    // 记录调用时间，帮助分析性能
                    this.logService.info(`IPC调用成功，耗时: ${Math.round(endTime - startTime)}ms`);
                    this.logService.info(`获取到的数据: ${JSON.stringify(detail)}`);

                    // 将结果发送给WebView
                    webview.postMessage({
                        command: 'caseDetail',
                        data: detail,
                        meta: {
                            timeCost: Math.round(endTime - startTime),
                            timestamp: new Date().toISOString(),
                            source: 'IPC'
                        }
                    });
                } catch (e) {
                    this.logService.error(`IPC通信失败: ${e.message || e}`);

                    // 给用户显示更友好、更详细的错误信息
                    webview.postMessage({
                        command: 'error',
                        text: `获取禅道用例详情失败: ${e.message || e}`,
                        detail: `错误详情: ${e.stack || 'No stack trace available'}`
                    });

                    // 为测试目的，可以尝试使用备用方法获取数据
                    this.logService.info('尝试使用备用方法获取数据...');
                    const cwd = process.cwd();
                    const scriptPath = cwd + '/src/vs/workbench/contrib/gat/browser/features/python/get_zentao_case.py';
                    await this.runPythonScript(scriptPath, caseId, webview);
                }
                return;
            } else {
                this.logService.warn(`收到未知命令: ${actualMessage.command}`);
            }
        });
    }

    /**
     * 递归查找给定目录下匹配文件名的YML文件
     */
    private async findYmlFilesByName(dirUri: URI, fileName: string): Promise<URI[]> {
        const result: URI[] = [];
        try {
            const dirEntries = await this.fileService.resolve(dirUri, { resolveMetadata: true });
            for (const entry of dirEntries.children || []) {
                if (entry.isDirectory) {
                    const subResults = await this.findYmlFilesByName(entry.resource, fileName);
                    result.push(...subResults);
                } else if (entry.name.toLowerCase() === fileName.toLowerCase()) {
                    result.push(entry.resource);
                }
            }
        } catch (e) {
            this.logService.error(`查找YAML文件时出错: ${e instanceof Error ? e.message : String(e)}`);
        }
        return result;
    }

    /**
     * 打开测试用例对应的YAML文件
     */
    public async openYmlFile(testCase: TestCase): Promise<void> {
        try {
            // 首先尝试使用testCase.ymlPath（如果有的话）
            if (testCase.ymlPath) {
                const uri = URI.parse(testCase.ymlPath as string);
                await this.editorService.openEditor({ resource: uri });
                return;
            }

            // 使用测试集路径+测试用例ID构建可能的yml文件路径
            if (this.currentTestSetPath) {
                // 如果当前测试集路径是JSON文件，获取它所在的目录
                let testSetDir: URI;
                if (this.currentTestSetPath.path.toLowerCase().endsWith('.json')) {
                    testSetDir = dirname(this.currentTestSetPath);
                } else {
                    testSetDir = this.currentTestSetPath;
                }

                // 尝试构建可能的yml文件路径
                const possibleYmlPath = joinPath(testSetDir, `${testCase.id}.yml`);
                this.logService.info(`尝试查找测试用例文件: ${possibleYmlPath.toString()}`);

                if (await this.fileService.exists(possibleYmlPath)) {
                    await this.editorService.openEditor({ resource: possibleYmlPath });
                    // 更新testCase.ymlPath以便将来使用
                    testCase.ymlPath = possibleYmlPath.toString();
                    return;
                }
            }

            // 回退方法：从工作区根目录开始搜索
            const workspaceFolders = this.workspaceContextService.getWorkspace().folders;
            if (!workspaceFolders.length) {
                this.logService.error('无法获取工作区目录');
                return;
            }
            const workspaceUri = workspaceFolders[0].uri;
            // 尝试查找testcase目录的多个可能位置
            const possibleTestcasePaths = [
                joinPath(workspaceUri, 'testcase'),
                joinPath(workspaceUri, 'extensions', 'testcase'),
                joinPath(workspaceUri, 'robot-testcase')
            ];
            const testcaseUri = possibleTestcasePaths[0]; // 默认使用第一个路径
            this.logService.info(`搜索测试用例文件: ${testcaseUri.toString()}, 文件名: ${testCase.id}.yml`);

            const matchingFiles = await this.findYmlFilesByName(testcaseUri, `${testCase.id}.yml`);
            if (matchingFiles.length > 0) {
                await this.editorService.openEditor({ resource: matchingFiles[0] });
                // 更新testCase.ymlPath以便将来使用
                testCase.ymlPath = matchingFiles[0].toString();
            } else {
                this.notificationService.error(localize('yamlNotFound', "未找到测试用例 YAML 文件: {0}", `${testCase.id}.yml`));
            }
        } catch (e) {
            this.logService.error(`打开 YAML 文件失败: ${e instanceof Error ? e.message : String(e)}`);
            this.notificationService.error(localize('openYmlError', "打开测试用例文件失败: {0}", e instanceof Error ? e.message : String(e)));
        }
    }

    /**
     * 重写renderBody方法，确保DOM结构正确
     */
    protected override renderBody(container: HTMLElement): void {
        super.renderBody(container);

        this.logService.info('开始渲染TestCaseView的body');

        // 清空容器，确保没有旧元素
        DOM.clearNode(container);
        this.logService.info('已清空容器');

        // 设置容器样式，确保内容可以正常滚动且不出现横向滚动
        container.style.overflow = 'auto';
        container.style.height = '100%';
        container.style.width = '100%';
        container.style.overflowX = 'hidden'; // 明确禁用水平滚动

        // 检查测试用例容器是否存在
        if (!this._testCaseElement) {
            this.logService.error('测试用例容器不存在');
            return;
        }

        // 将测试用例容器添加到视图body中
        container.appendChild(this._testCaseElement);
        this.logService.info('测试用例容器已添加到DOM中');

        // 移除测试用例容器上可能导致滚动条的样式
        this._testCaseElement.style.overflow = 'visible';
        this._testCaseElement.style.overflowX = 'hidden';
        this._testCaseElement.style.position = 'relative';
        this._testCaseElement.style.width = '100%';
        this._testCaseElement.style.minWidth = 'unset'; // 移除最小宽度限制

        // 检查列表容器是否存在
        if (!this.listContainer) {
            this.logService.error('列表容器不存在');
            return;
        }

        // 确保列表容器也不会创建自己的滚动区域
        this.listContainer.style.overflow = 'visible';
        this.listContainer.style.overflowX = 'hidden';
        this.listContainer.style.width = '100%';
        this.listContainer.style.minWidth = 'unset';

        // 检查当前测试用例数量
        this.logService.info(`当前测试用例数量: ${this._testCases.length}`);

        // 强制更新列表可见性
        this.updateListVisibility();

        // 强制刷新布局
        setTimeout(() => {
            if (this.testCaseList && this._testCases.length > 0) {
                try {
                    const height = this._testCaseElement.clientHeight;
                    this.logService.info(`延时刷新列表布局，高度: ${height}px`);
                    this.testCaseList.layout(height);
                } catch (error) {
                    this.logService.error(`刷新列表布局时出错: ${error instanceof Error ? error.message : String(error)}`);
                }
            }
        }, 100);
    }

    /**
     * 布局回调
     */
    protected override layoutBody(height: number, width: number): void {
        super.layoutBody(height, width);

        this.logService.debug(`TestCaseView布局调整: 高度=${height}px, 宽度=${width}px`);

        // 设置容器样式，传递宽度但不限制高度
        if (this._testCaseElement) {
            this._testCaseElement.style.width = '100%'; // 使用百分比而不是固定像素
            // 移除最小宽度限制
            this._testCaseElement.style.minWidth = 'unset';
            // 允许内容扩展到需要的高度
            this._testCaseElement.style.minHeight = '100%';
        }

        // 确保列表容器宽度
        if (this.listContainer) {
            this.listContainer.style.width = '100%';
            this.listContainer.style.minWidth = 'unset';
        }

        // 确保列表正确布局
        if (this.testCaseList && this._testCases.length > 0) {
            // 列表高度为所有项加起来的高度
            const itemHeight = 30; // 与delegate.getHeight一致
            const listHeight = this._testCases.length * itemHeight;
            this.testCaseList.layout(listHeight);
            this.logService.debug(`列表布局已刷新: 计算高度=${listHeight}px, 宽度=${width}px, 项目数=${this._testCases.length}`);
        }
    }

    /**
     * 焦点事件处理
     */
    public override focus(): void {
        super.focus();

        // 当视图获得焦点时，将焦点传递给列表
        if (this.testCaseList && this._testCases.length > 0) {
            this.testCaseList.domFocus();
        }
    }

    /**
     * 录制测试用例
     */
    private async recordTestCase(testCase: TestCase): Promise<void> {
        // Guard: 录制进行中时不允许切换用例
        if (this.isRecording && this.currentRecordingCaseId !== testCase.id) {
            this.notificationService.warn(localize('recordInProgress', "已有录制正在进行中，无法切换到其他测试用例"));
            return;
        }
        try {
            this.logService.info(`触发录制事件，测试用例ID: ${testCase.id}`);

            // 补充应用列表
            let appListForRecording: string[] = [];
            const existingAppList = (testCase as any).TestCaseAppList;
            if (Array.isArray(existingAppList) && existingAppList.length > 0) {
                appListForRecording = existingAppList;
            } else {
                // 从配置文件读取默认 AppName
                try {
                    this.logService.info('recordTestCase: appList 为空，尝试从配置文件获取默认应用对象');
                    const { fileService, logService } = this;
                    // 配置文件路径：testSetName_conf.json
                    let configUri: URI;
                    const pathStr = this.currentTestSetPath?.toString() || '';
                    if (pathStr.toLowerCase().endsWith('.json')) {
                        // currentPath 本身是配置文件
                        configUri = this.currentTestSetPath!;
                    } else {
                        // currentPath 是目录，配置文件位于父目录
                        configUri = joinPath(dirname(this.currentTestSetPath!), `${this.currentTestSetName}_conf.json`);
                    }
                    logService.info(`recordTestCase: 读取配置文件路径: ${configUri.toString()}`);
                    const content = await fileService.readFile(configUri);
                    const configJson = JSON.parse(content.value.toString());
                    const suiteConfig = configJson[this.currentTestSetName] || {};
                    const defaultApp = suiteConfig.AppName;
                    if (defaultApp && typeof defaultApp === 'string') {
                        appListForRecording.push(defaultApp);
                        logService.info(`recordTestCase: 使用默认应用对象: ${defaultApp}`);
                    }
                } catch (err) {
                    this.logService.warn(`recordTestCase: 读取默认应用对象失败: ${err}`);

                    // ---------- OCR fallback 逻辑 ----------
                    // 如果依然未获取到应用对象，尝试从测试用例信息推断
                    if (appListForRecording.length === 0) {
                        const featureField = (testCase as any).feature;
                        const driverField = (testCase as any).driver;

                        const inferredApp = (typeof featureField === "string" && featureField.trim())
                            ? featureField.trim()
                            : (typeof driverField === "string" && driverField.trim() ? driverField.trim() : undefined);

                        if (inferredApp) {
                            appListForRecording.push(inferredApp);
                            this.logService.info(`recordTestCase: 推断应用对象为: ${inferredApp}`);
                        } else if ((testCase as any).TestCaseType && String((testCase as any).TestCaseType).toUpperCase() === "OCR" && this.currentTestSetName.trim()) {
                            appListForRecording.push(this.currentTestSetName.trim());
                            this.logService.info(`recordTestCase: OCR 场景 fallback 使用测试集名称作为应用对象: ${this.currentTestSetName}`);
                        }
                    }
                }
            }

            // 构造带有应用列表的测试用例对象
            const enrichedTestCase = { ...testCase, TestCaseAppList: appListForRecording };

            // 直接触发事件，将带应用列表的测试用例传递给TestCasePlayer处理
            const event = new CustomEvent('gat:record-testcase', {
                detail: { testCase: enrichedTestCase },
                bubbles: true,
                cancelable: true
            });

            document.dispatchEvent(event);
            this.logService.info(`已触发录制测试用例事件: ${testCase.id}`);

        } catch (error) {
            this.logService.error(`触发录制测试用例事件时出错: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * 更新所有录制按钮状态，禁止在其他用例上点击
     */
    private updateRecordButtons(): void {
        if (!this.listContainer) {
            return;
        }
        this.listContainer.querySelectorAll<HTMLElement>('.test-case-action-record').forEach(btn => {
            const item = btn.closest('.test-case-item');
            const idElem = item?.querySelector<HTMLElement>('.test-case-id');
            const id = idElem?.textContent;
            if (this.isRecording && id !== this.currentRecordingCaseId) {
                btn.style.pointerEvents = 'none';
                btn.style.opacity = '0.5';
            } else {
                btn.style.pointerEvents = 'auto';
                btn.style.opacity = '1';
            }
        });
    }

    /**
     * 播放测试用例
     */
    private async playTestCase(testCase: TestCase): Promise<void> {
        try {
            this.logService.info(`触发播放事件，测试用例ID: ${testCase.id}`);

            // 直接触发事件，将路径查找逻辑交给TestCasePlayer处理
            const event = new CustomEvent('gat:play-testcase', {
                detail: {
                    testCase: { id: testCase.id, name: testCase.name, story: testCase.story } // 只传递必要信息
                },
                bubbles: true,
                cancelable: true
            });

            document.dispatchEvent(event);
            this.logService.info(`已触发播放测试用例事件: ${testCase.id}`);

        } catch (error) {
            this.logService.error(`触发播放测试用例事件时出错: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * 删除测试用例
     */
    private async deleteTestCase(testCase: TestCase): Promise<void> {
        try {
            this.logService.info(`准备删除测试用例: ${testCase.id}`);

            // 显示确认对话框
            const confirmed = await this.showDeleteConfirmationDialog(testCase);
            if (!confirmed) {
                this.logService.info(`用户取消删除测试用例: ${testCase.id}`);
                return;
            }

            this.logService.info(`用户确认删除测试用例: ${testCase.id}，开始删除操作`);

            // 1. 删除 YAML 文件
            await this.deleteTestCaseFile(testCase);

            // 2. 从配置文件中删除对应节点
            await this.removeTestCaseFromConfig(testCase);

            // 3. 刷新测试用例列表
            this.logService.info(`开始刷新测试用例列表，当前测试集: ${this.currentTestSetName}`);
            this.refreshTestCaseList(this.currentTestSetName);

            // 4. 显示成功消息
            this.notificationService.info(localize('testCaseDeleted', '测试用例 {0} 删除成功！', testCase.id));

            this.logService.info(`测试用例删除完成: ${testCase.id}`);

        } catch (error) {
            this.logService.error(`删除测试用例时出错: ${error instanceof Error ? error.message : String(error)}`);
            this.notificationService.error(localize('testCaseDeleteFailed', '删除测试用例失败: {0}', error instanceof Error ? error.message : String(error)));
        }
    }

    /**
     * 显示删除确认对话框
     */
    private async showDeleteConfirmationDialog(testCase: TestCase): Promise<boolean> {
        return new Promise((resolve) => {
            // 创建确认对话框的HTML
            const dialogHtml = `
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
                    <title>确认删除</title>
                    <style>
                        body {
                            font-family: var(--vscode-font-family);
                            font-size: var(--vscode-font-size);
                            color: var(--vscode-foreground);
                            background-color: var(--vscode-editor-background);
                            padding: 20px;
                            margin: 0;
                        }
                        .container {
                            max-width: 500px;
                            margin: 0 auto;
                        }
                        h1 {
                            color: var(--vscode-errorForeground);
                            font-size: 1.5em;
                            margin-bottom: 15px;
                            border-bottom: 1px solid var(--vscode-panel-border);
                            padding-bottom: 10px;
                        }
                        .icon {
                            margin-right: 10px;
                            font-size: 1.2em;
                        }
                        .message {
                            margin-bottom: 20px;
                            line-height: 1.5;
                        }
                        .test-case-info {
                            background-color: var(--vscode-textBlockQuote-background);
                            border-left: 4px solid var(--vscode-textBlockQuote-border);
                            padding: 12px;
                            margin: 15px 0;
                        }
                        .button-container {
                            display: flex;
                            justify-content: flex-end;
                            gap: 10px;
                        }
                        button {
                            padding: 8px 16px;
                            border: none;
                            border-radius: 2px;
                            cursor: pointer;
                        }
                        .cancel-button {
                            background-color: var(--vscode-button-secondaryBackground);
                            color: var(--vscode-button-secondaryForeground);
                        }
                        .cancel-button:hover {
                            background-color: var(--vscode-button-secondaryHoverBackground);
                        }
                        .delete-button {
                            background-color: var(--vscode-errorForeground);
                            color: var(--vscode-editor-background);
                        }
                        .delete-button:hover {
                            opacity: 0.9;
                        }
                        .warning {
                            color: var(--vscode-editorWarning-foreground);
                            font-weight: bold;
                            margin-top: 10px;
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>
                            <span class="icon">⚠️</span>
                            确认删除测试用例
                        </h1>
                        <div class="message">
                            您确定要删除以下测试用例吗？
                        </div>
                        <div class="test-case-info">
                            <strong>测试用例 ID：</strong>${testCase.id}<br>
                            <strong>测试用例名称：</strong>${testCase.name || '未指定'}<br>
                            			${testCase.story ? `<strong>描述\uff1a</strong>${testCase.story}<br>` : ''}
                        </div>
                        <div class="warning">
                            ⚠️ 此操作将永久删除测试用例文件和配置信息，无法恢复！
                        </div>
                        <div class="button-container">
                            <button type="button" class="cancel-button" id="cancelButton">取消</button>
                            <button type="button" class="delete-button" id="deleteButton">确认删除</button>
                        </div>
                    </div>

                    <script>
                        const vscode = acquireVsCodeApi();

                        document.getElementById('cancelButton').addEventListener('click', () => {
                            vscode.postMessage({ command: 'cancel' });
                        });

                        document.getElementById('deleteButton').addEventListener('click', () => {
                            vscode.postMessage({ command: 'confirm' });
                        });
                    </script>
                </body>
                </html>
            `;

            // 创建WebView
            const webviewPanel = this.webviewWorkbenchService.openWebview({
                title: localize('confirmDelete', '确认删除'),
                options: {
                    enableFindWidget: false,
                    retainContextWhenHidden: true
                },
                contentOptions: {
                    allowScripts: true,
                    localResourceRoots: []
                },
                extension: undefined
            }, 'deleteConfirmWebview', localize('confirmDelete', '确认删除'), {});

            // 设置WebView内容
            const webview = webviewPanel.webview;
            webview.setHtml(dialogHtml);

            // 处理WebView消息
            const messageListener = webview.onMessage((message: any) => {
                const actualMessage = message.message || message;

                if (actualMessage.command === 'confirm') {
                    messageListener.dispose();
                    webviewPanel.dispose();
                    resolve(true);
                } else if (actualMessage.command === 'cancel') {
                    messageListener.dispose();
                    webviewPanel.dispose();
                    resolve(false);
                }
            });

            // 设置超时，如果5分钟内没有响应，自动取消
            setTimeout(() => {
                messageListener.dispose();
                if (!webviewPanel.isDisposed) {
                    webviewPanel.dispose();
                }
                resolve(false);
            }, 300000); // 5分钟超时
        });
    }

    /**
     * 删除测试用例文件
     */
    private async deleteTestCaseFile(testCase: TestCase): Promise<void> {
        try {
            // 确定文件路径
            let fileUri: URI;

            if (testCase.ymlPath) {
                // 如果测试用例有指定路径，使用该路径
                fileUri = URI.parse(testCase.ymlPath as string);
            } else {
                // 使用当前测试集路径构建文件路径
                if (this.currentTestSetPath) {
                    const pathStr = this.currentTestSetPath.toString();
                    const isJsonFile = pathStr.toLowerCase().endsWith('.json') || pathStr.toLowerCase().endsWith('_conf.json');

                    let testcaseDirPath: URI;
                    if (isJsonFile) {
                        // currentPath 本身是配置文件
                        testcaseDirPath = dirname(this.currentTestSetPath);
                    } else {
                        // 如果不是配置文件，直接使用该路径
                        testcaseDirPath = this.currentTestSetPath;
                    }

                    fileUri = joinPath(testcaseDirPath, `${testCase.id}.yml`);
                } else {
                    // 备用方案：使用路径解析器获取测试用例路径
                    const testcaseUri = await this.pathResolver.getTestcasePath();
                    if (!testcaseUri) {
                        throw new Error('无法确定测试用例文件路径');
                    }
                    fileUri = joinPath(testcaseUri, this.currentTestSetName, `${testCase.id}.yml`);
                }
            }

            this.logService.info(`准备删除测试用例文件: ${fileUri.toString()}`);

            // 检查文件是否存在
            const exists = await this.fileService.exists(fileUri);
            if (!exists) {
                this.logService.warn(`测试用例文件不存在: ${fileUri.toString()}`);
                return;
            }

            // 删除文件
            await this.fileService.del(fileUri);
            this.logService.info(`测试用例文件删除成功: ${fileUri.toString()}`);

        } catch (error) {
            this.logService.error(`删除测试用例文件失败: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }

    /**
     * 从配置文件中删除测试用例节点
     */
    private async removeTestCaseFromConfig(testCase: TestCase): Promise<void> {
        try {
            // 确定配置文件路径
            let configFilePath: URI;

            if (this.currentTestSetPath && this.currentTestSetPath.toString().toLowerCase().endsWith('.json')) {
                // 如果当前路径就是配置文件
                configFilePath = this.currentTestSetPath;
            } else {
                // 构建配置文件路径
                let testcaseBasePath: URI;

                if (this.currentTestSetPath) {
                    testcaseBasePath = dirname(this.currentTestSetPath);
                } else {
                    // 使用路径解析器获取测试用例路径
                    const resolvedPath = await this.pathResolver.getTestcasePath();
                    if (!resolvedPath) {
                        throw new Error('未找到有效的测试用例路径');
                    }
                    testcaseBasePath = resolvedPath;
                }

                configFilePath = joinPath(testcaseBasePath, `${this.currentTestSetName}_conf.json`);
            }

            this.logService.info(`准备从配置文件中删除测试用例: ${configFilePath.toString()}`);

            // 检查配置文件是否存在
            const exists = await this.fileService.exists(configFilePath);
            if (!exists) {
                this.logService.warn(`配置文件不存在: ${configFilePath.toString()}`);
                return;
            }

            // 读取配置文件
            const fileContent = await this.fileService.readFile(configFilePath);
            const config = JSON.parse(fileContent.value.toString());

            // 从配置中删除测试用例节点
            if (config[this.currentTestSetName] && config[this.currentTestSetName][testCase.id]) {
                delete config[this.currentTestSetName][testCase.id];
                this.logService.info(`从配置中删除测试用例节点: ${testCase.id}`);

                // 写回配置文件
                await this.textFileService.write(configFilePath, JSON.stringify(config, null, 4));
                this.logService.info(`配置文件更新成功: ${configFilePath.toString()}`);
            } else {
                this.logService.warn(`配置文件中未找到测试用例节点: ${testCase.id}`);
            }

        } catch (error) {
            this.logService.error(`从配置文件中删除测试用例节点失败: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }

    /**
     * 重写 dispose 方法，确保清理所有资源
     */
    public override dispose(): void {
        // 调用父类方法
        super.dispose();
    }

    /**
     * 设置当前测试集名称
     */
    public setCurrentTestSetName(testSetName: string): void {
        this.currentTestSetName = testSetName;
    }

    /**
     * 设置当前测试集路径
     */
    public setCurrentTestSetPath(testSetPath: URI): void {
        this.logService.info(`设置当前测试集路径: ${testSetPath.toString()}`);
        this.currentTestSetPath = testSetPath;
    }

    /**
     * 创建测试用例YAML文件
     */
    private async createTestCaseFile(testSetName: string, testCaseData: TestCaseData): Promise<URI> {
        try {
            this.logService.info('🔧 开始创建测试用例文件');
            console.log('🔧 createTestCaseFile 开始:', { testSetName, testCaseData });

            // 验证输入数据
            if (!testCaseData.id || !testCaseData.name || !testCaseData.module) {
                const error = new Error(`测试用例数据不完整: ID=${testCaseData.id}, Name=${testCaseData.name}, Module=${testCaseData.module}`);
                this.logService.error('❌ 测试用例数据验证失败:', error);
                console.error('❌ 测试用例数据验证失败:', error);
                throw error;
            }

            this.logService.info('✅ 测试用例数据验证通过');

            // 优先使用当前测试集的实际路径
            let testcaseDirPath: URI;
            let testcaseBasePath: URI;
            // 存储实际的配置文件路径，用于后续更新
            let actualConfigFilePath: URI | undefined;

            if (this.currentTestSetPath) {
                this.logService.info(`原始测试集路径: ${this.currentTestSetPath.toString()}`);

                // 检查路径是否指向配置文件（.json文件）
                const pathStr = this.currentTestSetPath.toString();
                const isJsonFile = pathStr.toLowerCase().endsWith('.json') || pathStr.toLowerCase().endsWith('_conf.json');

                if (isJsonFile) {
                    // currentPath 本身是配置文件
                    actualConfigFilePath = this.currentTestSetPath;
                    // 使用其所在的目录作为基础路径
                    testcaseBasePath = dirname(this.currentTestSetPath);
                    // 测试用例目录直接使用配置文件所在的目录，不再使用 testSetName 创建子目录
                    testcaseDirPath = testcaseBasePath;
                    this.logService.info(`检测到路径指向配置文件，使用其所在目录作为测试用例目录: ${testcaseDirPath.toString()}`);
                } else {
                    // 如果不是配置文件，直接使用该路径作为测试用例目录
                    testcaseDirPath = this.currentTestSetPath;
                    testcaseBasePath = dirname(testcaseDirPath);
                    this.logService.info(`使用非配置文件路径作为测试用例目录: ${testcaseDirPath.toString()}`);

                    // 如果路径包含测试集名称，提取其基础路径
                    const pathParts = testcaseDirPath.path.split('/');
                    const testSetNameIndex = pathParts.findIndex(part => part === testSetName);
                    if (testSetNameIndex > 0) {
                        // 构建基础路径（去掉测试集名称部分）
                        const basePath = pathParts.slice(0, testSetNameIndex).join('/');
                        testcaseBasePath = this.currentTestSetPath.with({ path: basePath });
                        this.logService.info(`从测试集路径中提取基础路径: ${testcaseBasePath.toString()}`);
                    } else {
                        // 如果无法从路径中提取基础路径，则使用父目录
                        testcaseBasePath = dirname(this.currentTestSetPath);
                        this.logService.info(`使用测试集的父目录作为基础路径: ${testcaseBasePath.toString()}`);
                    }
                }
            } else {
                this.logService.info('未设置当前测试集路径，使用路径解析器获取路径');

                // 使用路径解析器获取可写的基础路径
                const writableBasePath = await this.pathResolver.getWritableBasePath();
                if (!writableBasePath) {
                    // 如果没有可写路径，尝试获取测试用例路径
                    const testcasePath = await this.pathResolver.getTestcasePath();
                    if (!testcasePath) {
                        throw new Error('无法获取有效的测试用例路径');
                    }
                    testcaseBasePath = testcasePath;
                    testcaseDirPath = joinPath(testcasePath, testSetName);
                } else {
                    // 如果获取的是 KylinRobot-v2 根目录，需要添加 testcase 子目录
                    const testcasePath = writableBasePath.path.endsWith('/testcase') ? writableBasePath : joinPath(writableBasePath, 'testcase');
                    testcaseBasePath = testcasePath;
                    testcaseDirPath = joinPath(testcasePath, testSetName);
                }
            }

            // 构建测试用例文件路径
            const testcaseFilePath = joinPath(testcaseDirPath, `${testCaseData.id}.yml`);

            this.logService.info(`测试用例目录路径: ${testcaseDirPath.toString()}`);
            this.logService.info(`测试用例文件路径: ${testcaseFilePath.toString()}`);

            // 确保测试集目录存在
            try {
                await this.fileService.stat(testcaseDirPath);
                this.logService.info(`测试集目录存在: ${testcaseDirPath.toString()}`);
            } catch (error) {
                // 如果目录不存在，则尝试创建它 (包括所有父目录)
                this.logService.warn(`测试集目录不存在: ${testcaseDirPath.toString()}，将尝试创建该目录。`);
                try {
                    await this.fileService.createFolder(testcaseDirPath);
                    this.logService.info(`成功创建测试集目录: ${testcaseDirPath.toString()}`);
                } catch (createError) {
                    this.logService.error(`创建测试集目录失败: ${testcaseDirPath.toString()}`, createError);
                    // 如果创建目录也失败了，再抛出错误
                    throw new Error(`创建测试集目录失败: ${testcaseDirPath.toString()}`);
                }
            }

            // 生成YAML内容
            this.logService.info('📝 开始生成YAML内容');
            console.log('📝 开始生成YAML内容，输入数据:', testCaseData);
            const yamlContent = this.generateTestCaseYaml(testCaseData);
            this.logService.info('📝 YAML内容生成完成');
            console.log('📝 生成的YAML内容:', yamlContent);

            // 写入文件
            this.logService.info(`💾 开始写入YAML文件: ${testcaseFilePath.toString()}`);
            console.log('💾 准备写入文件:', testcaseFilePath.toString());
            await this.textFileService.write(testcaseFilePath, yamlContent);
            this.logService.info(`✅ YAML文件写入成功: ${testcaseFilePath.toString()}`);
            console.log('✅ 文件写入成功');

            // 如果存在配置文件（GAT 类型），则更新；
            // OCR 类型测试集通常没有 *_conf.json，直接依赖目录内 YAML，无需生成配置文件
            if (actualConfigFilePath) {
                this.logService.info(`⚙️ 开始更新测试集配置文件: ${actualConfigFilePath.toString()}`);
                console.log('⚙️ 开始更新配置文件');
                await this.updateTestSetConfig(testSetName, testCaseData, testcaseBasePath, actualConfigFilePath);
                this.logService.info(`✅ 测试集配置文件更新成功`);
                console.log('✅ 配置文件更新成功');
            } else {
                this.logService.info('🛈 OCR 测试集（无现有配置文件），跳过 *_conf.json 更新/创建');
            }

            // 返回创建的测试用例文件路径
            this.logService.info(`🎯 返回创建的测试用例文件路径: ${testcaseFilePath.toString()}`);
            return testcaseFilePath;

        } catch (error) {
            this.logService.error('创建测试用例文件失败:', error);
            throw error;
        }
    }

    /**
     * 生成测试用例YAML内容
     */
    private generateTestCaseYaml(testCaseData: TestCaseData): string {
        // 清理测试用例数据，确保格式正确
        const cleanedData = TextFormatCleaner.cleanTestCaseData(testCaseData);

        // 提取 feature: module 中最后一个 / 后面的部分，如果没有 / 则使用整个 module
        const moduleParts = cleanedData.module.split('/');
        const feature = moduleParts.length > 0 ? moduleParts[moduleParts.length - 1] : cleanedData.module;

        // 构建 steps YAML 字符串
        let stepsYaml = '';
        const hasValidSteps = Array.isArray(cleanedData.steps) && cleanedData.steps.length > 0 &&
            cleanedData.steps.some((step: any) => step.desc && step.desc.trim());

        if (hasValidSteps) {
            stepsYaml = '    steps:\n';
            for (const step of cleanedData.steps) {
                // 只处理有描述内容的步骤
                if (step.desc && step.desc.trim()) {
                    const cleanDesc = step.desc.trim();
                    const stepKey = `步骤${step.index}\uff1a${cleanDesc}`;
                    stepsYaml += `      - ${stepKey}:\n`;
                }
            }
            this.logService.info(`生成用例步骤YAML内容，步骤数量: ${cleanedData.steps.length}`);
        } else {
            // 没有有效步骤时，生成默认的步骤1，供录制时填充
            stepsYaml = '    steps:\n      - 步骤1\uff1a请录制步骤:\n';
            this.logService.info('没有有效的用例步骤，已生成默认步骤1');
        }

        // 使用清理后的数据生成YAML内容，确保字符串安全
        const safeFeature = TextFormatCleaner.escapeYamlString(feature);
        const safeName = TextFormatCleaner.escapeYamlString(cleanedData.name || cleanedData.title);
        const safeModule = TextFormatCleaner.escapeYamlString(cleanedData.module);

        // 确保测试用例ID直接使用数字格式，不加引号（即使是纯数字）
        // 对于YAML根键，纯数字ID应该直接使用，不需要引号包围
        const safeId = cleanedData.id; // 直接使用原始ID，不经过escapeYamlString处理

        // 构建custom_loglevel字段（如果启用人工校验）
        const customLogLevel = cleanedData.manualVerification ? '    custom_loglevel: defined\n' : '';

        // 构建自定义参数YAML内容
        let customParamsYaml = '';
        if (cleanedData.customParameters && cleanedData.customParameters.length > 0) {
            try {
                this.logService.info(`开始处理自定义参数，数量: ${cleanedData.customParameters.length}`);
                for (const param of cleanedData.customParameters) {
                    if (param.key && param.key.trim()) {
                        const paramKey = TextFormatCleaner.escapeYamlString(param.key.trim());
                        let paramValue = param.value || '';

                        // 根据参数类型处理值
                        switch (param.type) {
                            case 'int':
                                paramValue = paramValue.trim() === '' ? '0' : paramValue;
                                break;
                            case 'bool':
                                paramValue = paramValue.toLowerCase() === 'true' ? 'true' : 'false';
                                break;
                            case 'list':
                                // 如果是列表类型，尝试解析或使用默认空列表
                                if (paramValue.trim() === '') {
                                    paramValue = '[]';
                                } else if (!paramValue.trim().startsWith('[')) {
                                    paramValue = `[${paramValue}]`;
                                }
                                break;
                            case 'dict':
                                // 如果是字典类型，尝试解析或使用默认空字典
                                if (paramValue.trim() === '') {
                                    paramValue = '{}';
                                } else if (!paramValue.trim().startsWith('{')) {
                                    paramValue = `{${paramValue}}`;
                                }
                                break;
                            default:
                                // 字符串类型
                                paramValue = TextFormatCleaner.escapeYamlString(paramValue);
                                break;
                        }

                        customParamsYaml += `    ${paramKey}: ${paramValue} # 自定义参数\n`;
                        this.logService.info(`处理自定义参数: ${paramKey} = ${paramValue} (${param.type})`);
                    }
                }
                this.logService.info(`自定义参数处理完成，生成内容: \n${customParamsYaml}`);
            } catch (error) {
                this.logService.error(`处理自定义参数时出错: ${error instanceof Error ? error.message : String(error)}`);
                // 出错时继续处理，但跳过自定义参数
                customParamsYaml = '';
            }
        }

        cleanedData.author = this.customAuthenticationService.getUsername();

        // 根据新规则生成YAML内容，使用Label替代record_type，移除priority和exec_priority
        let yamlContent = `${safeId}:
${customLogLevel}    feature: ${safeFeature} # 使用提取的 feature
    story: ${safeName} # 直接使用 title/name 作为 story
    modelpath: ${safeModule}
    author: ${cleanedData.author || 'kylinrobot'}
    label: ${cleanedData.recordType || 'UNI'}
${customParamsYaml}    setup:
    teardown:`;

        // 总是添加steps字段，确保测试用例结构完整
        yamlContent += `\n${stepsYaml.trimEnd()} # 使用动态生成的 steps YAML`;

        return yamlContent + '\n';
    }

    /**
     * 更新测试集配置文件
     */
    private async updateTestSetConfig(testSetName: string, testCaseData: TestCaseData, testcaseBasePath: URI, actualConfigFilePath?: URI): Promise<void> {
        try {
            // 构建配置文件路径 - 如果有传入实际配置文件路径，则使用它
            const configFilePath = actualConfigFilePath || joinPath(testcaseBasePath, `${testSetName}_conf.json`);
            this.logService.info(`测试集配置文件路径: ${configFilePath.toString()}`);

            // 读取现有配置
            let config: any = {};
            try {
                const fileContent = await this.fileService.readFile(configFilePath);
                config = JSON.parse(fileContent.value.toString());
            } catch (error) {
                // 文件不存在，创建新配置
                config = {};
            }

            // 更新配置（保留已有字段，只添加或更新必要的顶层字段）
            if (!config[testSetName]) {
                config[testSetName] = {};
            }
            const testSetConfig = config[testSetName];
            // 确保顶层字段存在
            testSetConfig.AppName = testSetConfig.AppName || 'KylinRobot';
            testSetConfig.TestSuitName = testSetName;

            // 构建新测试用例条目
            const newCase = {
                TestCaseID: testCaseData.id,
                TestCaseAppList: testCaseData.driver, // 新增应用列表
                TestCaseName: testCaseData.name,
                TestCaseModelpath: testCaseData.module,
                TestCaseScriptName: `/${testCaseData.id}.yml`,
                TestCaseScriptPath: `/${testCaseData.id}_${testCaseData.id}`,
                TestCaseType: testCaseData.recordType,
                TestCaseSteps: testCaseData.steps || [],
            };

            // 插入或覆盖测试用例
            testSetConfig[testCaseData.id] = newCase;

            // 只保留当前测试集节点，写回配置文件
            const output: any = {};
            output[testSetName] = testSetConfig;
            await this.textFileService.write(configFilePath, JSON.stringify(output, null, 4));

        } catch (error) {
            this.logService.error('更新测试集配置文件失败:', error);
            throw error;
        }
    }

    /**
     * 录制测试用例
     */
    private startRecording(testCase: any): void {
        // 发送录制事件
        const event = new CustomEvent('gat:record-testcase', {
            detail: { testCase },
            bubbles: true,
            cancelable: true
        });
        document.dispatchEvent(event);
    }

    /**
     * 刷新测试用例列表
     */
    private refreshTestCaseList(testSetName: string): void {
        this.logService.info(`发送刷新测试用例列表事件: ${testSetName}`);

        // 发送自定义事件，通知测试用例视图刷新
        const event = new CustomEvent('gat:refresh-testcases', {
            detail: { testSetName },
            bubbles: true,
            cancelable: true
        });
        document.dispatchEvent(event);

        this.logService.info(`刷新事件已发送`);
    }

    /**
     * 运行Python脚本作为备用方法获取数据
     * 这是针对IPC通信失败时的备用方案
     */
    private async runPythonScript(scriptPath: string, caseId: string, webview: any): Promise<void> {
        this.logService.info(`尝试通过IPC服务运行Python脚本: ${scriptPath} --case ${caseId} --mock`);

        try {
            // 使用禅道服务获取数据
            const result = await this.zenTaoService.getCaseDetail(caseId);

            if (result) {
                webview.postMessage({
                    command: 'caseDetail',
                    data: result,
                    meta: {
                        source: 'ZenTaoService',
                        exitCode: 0
                    }
                });
            } else {
                // 当返回值为空时发送错误消息
                webview.postMessage({
                    command: 'error',
                    text: '未能获取测试用例详情',
                    detail: '通过ZenTaoService获取数据失败'
                });
            }
        } catch (e) {
            this.logService.error(`获取测试用例详情失败: ${e}`);
            // 确保错误消息被正确传递给WebView
            try {
                webview.postMessage({
                    command: 'error',
                    text: '获取测试用例详情失败',
                    detail: e instanceof Error ? e.message : String(e)
                });
            } catch (postError) {
                // 如果发送消息也失败，确保至少发送一个基本错误消息
                this.logService.error(`向WebView发送错误消息失败: ${postError}`);
                try {
                    webview.postMessage({
                        command: 'error',
                        text: '系统错误',
                        detail: '无法获取测试用例详情，请手动填写必要信息'
                    });
                } catch (finalError) {
                    this.logService.error(`WebView通信完全失败: ${finalError}`);
                }
            }
        }
    }

    private async promptRecordModeAndRecordTestCase(testCase: TestCase): Promise<void> {
        // 先在编辑器中打开测试用例文件
        try {
            this.logService.info(`打开测试用例文件: ${testCase.id}`);
            await this.openYmlFile(testCase);
        } catch (error) {
            this.logService.error(`打开测试用例文件失败: ${error instanceof Error ? error.message : String(error)}`);
            // 即使打开文件失败，我们也继续录制流程，不阻断用户操作
        }

        const items: IQuickPickItem[] = [
            { label: localize('continueRecording', "继续录制") },
            { label: localize('overwriteRecording', "覆盖当前用例") }
        ];

        const choice = await this.quickInputService.pick(items, {
            canPickMany: false,
            placeHolder: localize('selectRecordMode', "请选择录制形式")
        });

        if (choice) {
            if (choice.label === localize('continueRecording', "继续录制")) {
                this.recordTestCase(testCase);
            } else if (choice.label === localize('overwriteRecording', "覆盖当前用例")) {
                // 清空 actions 的逻辑待实现
                this.logService.info(`选择覆盖当前用例: ${testCase.id}`);
                // 假设清空操作是异步的，如果不是，可以移除 await
                await this.clearTestCaseActions(testCase);
                this.recordTestCase(testCase);
            }
        }
    }

    private async clearTestCaseActions(testCase: TestCase): Promise<void> {
        this.logService.info(`准备清空测试用例 ${testCase.id} 的 actions`);

        // 重新构建正确的YAML文件路径，不依赖可能错误的 testCase.ymlPath
        let ymlUri: URI;

        try {
            // 优先使用当前测试集路径构建文件路径
            if (this.currentTestSetPath) {
                const pathStr = this.currentTestSetPath.toString();
                const isJsonFile = pathStr.toLowerCase().endsWith('.json') || pathStr.toLowerCase().endsWith('_conf.json');

                let testcaseDirPath: URI;
                if (isJsonFile) {
                    // 如果是配置文件，使用其所在的目录
                    testcaseDirPath = dirname(this.currentTestSetPath);
                } else {
                    // 如果不是配置文件，直接使用该路径
                    testcaseDirPath = this.currentTestSetPath;
                }

                ymlUri = joinPath(testcaseDirPath, `${testCase.id}.yml`);
                this.logService.info(`使用当前测试集路径构建YAML文件路径: ${ymlUri.toString()}`);
            } else {
                // 备用方案：使用路径解析器获取测试用例路径
                const testcaseUri = await this.pathResolver.getTestcasePath();
                if (!testcaseUri) {
                    throw new Error('无法确定测试用例文件路径');
                }
                ymlUri = joinPath(testcaseUri, this.currentTestSetName, `${testCase.id}.yml`);
                this.logService.info(`使用路径解析器构建YAML文件路径: ${ymlUri.toString()}`);


            }

            // 检查文件是否存在
            const exists = await this.fileService.exists(ymlUri);
            if (!exists) {
                this.notificationService.error(localize('ymlFileNotFound', "无法清空测试用例 {0} 的动作：YAML文件不存在 {1}", testCase.id, ymlUri.toString()));
                return;
            }

            // 使用标准js-yaml库解析YAML（保持完整结构）
            const fileContent = await this.textFileService.read(ymlUri);
            let ymlObject: any;

            try {
                // 使用全局的js-yaml解析
                ymlObject = (window as any).jsyaml.load(fileContent.value);
            } catch (parseError) {
                this.logService.error(`js-yaml解析失败: ${parseError}`);
                this.notificationService.error(localize('yamlParseError', "无法解析YAML文件: {0}", parseError.message));
                return;
            }

            // 确保 ymlObject 是对象并且包含测试用例ID作为键
            if (typeof ymlObject !== 'object' || ymlObject === null || !ymlObject[testCase.id]) {
                this.notificationService.error(localize('invalidYmlFormat', "无法清空测试用例 {0} 的动作：YAML文件格式无效或未找到用例ID。", testCase.id));
                return;
            }

            const testCaseSpecificData = ymlObject[testCase.id];

            // 清空 setup 和 teardown
            testCaseSpecificData.setup = null;
            testCaseSpecificData.teardown = null;
            this.logService.info(`已将用例 ${testCase.id} 的 setup 和 teardown 设置为 null。`);

            // 处理 steps，只保留步骤描述，清空具体的action内容
            if (testCaseSpecificData.steps && Array.isArray(testCaseSpecificData.steps)) {
                const processedSteps: any[] = [];

                for (const step of testCaseSpecificData.steps) {
                    if (typeof step === 'object' && step !== null) {
                        const stepKeys = Object.keys(step);
                        if (stepKeys.length > 0) {
                            const stepKey = stepKeys[0];

                            // 只保留有意义的步骤描述（步骤标题）
                            if (stepKey &&
                                stepKey.trim() !== '' &&
                                stepKey !== 'action' &&
                                (stepKey.includes('步骤') || stepKey.includes('Gat') || stepKey.length > 5)) {

                                // 保留步骤描述，但清空其内容
                                processedSteps.push({ [stepKey]: null });
                                this.logService.info(`保留步骤: "${stepKey}"`);
                            } else {
                                this.logService.info(`跳过无意义条目: "${stepKey}"`);
                            }
                        }
                    }
                }

                if (processedSteps.length > 0) {
                    testCaseSpecificData.steps = processedSteps;
                    this.logService.info(`已清空 ${processedSteps.length} 个步骤的动作内容。`);
                } else {
                    // 如果没有有效的步骤，创建一个默认步骤
                    testCaseSpecificData.steps = [
                        { [localize('defaultStepName', "步骤1\uff1a请录制步骤")]: null }
                    ];
                    this.logService.info('未找到有效步骤，已创建一个默认步骤。');
                }
            } else {
                // 如果steps不存在或不是数组，创建默认步骤
                testCaseSpecificData.steps = [
                    { [localize('defaultStepName', "步骤1\uff1a请录制步骤")]: null }
                ];
                this.logService.info('steps不存在，已创建默认步骤结构。');
            }

            // 使用标准js-yaml生成YAML
            let newYamlContent = (window as any).jsyaml.dump(ymlObject, {
                indent: 4,
                lineWidth: -1,
                noRefs: true,
                quotingType: '"',
                forceQuotes: false
            });

            // 后处理：移除纯数字键的引号（只针对测试用例ID）
            newYamlContent = newYamlContent.replace(/^(['"]?)(\d+)\1:/gm, '$2:');

            // 后处理：修复steps数组格式，确保 "- " 与步骤内容在同一行并正确对齐
            newYamlContent = newYamlContent.replace(/^(\s+)-\s*\n(\s+)(.+):/gm, (match: string, indent1: string, indent2: string, stepContent: string) => {
                // 计算正确的缩进（steps下的项目应该是4个空格缩进+2个空格给'-'）
                const correctIndent = '        '; // 8个空格（steps缩进4+数组项缩进4）
                return `${correctIndent}- ${stepContent}:`;
            });

            await this.textFileService.write(ymlUri, newYamlContent);
            this.notificationService.info(localize('actionsCleared', "测试用例 {0} 的动作已清空，保留步骤结构。", testCase.id));

        } catch (error: any) {
            this.logService.error(`清空测试用例 ${testCase.id} 的 actions 失败: ${error?.message || error}`);
            this.notificationService.error(localize('clearActionsError', "清空测试用例 {0} 的动作时发生错误: {1}", testCase.id, error?.message || String(error)));
        }
    }

    /**
     * 辅助方法：查找并设置测试用例的YAML文件路径
     */
    private async findPathAndSet(element: TestCase): Promise<URI | undefined> {
        // 使用路径解析器获取测试用例目录
        const testcaseUri = await this.pathResolver.getTestcasePath();
        if (!testcaseUri) {
            this.logService.error('无法获取有效的测试用例路径，无法加载YAML文件');
            return undefined;
        }

        const matchingFiles = await this.findYmlFilesByName(testcaseUri, `${element.id}.yml`);

        if (!matchingFiles.length) {
            this.logService.error(`辅助方法未找到YAML文件: ${element.id}.yml`);
            return undefined;
        }

        const yamlUri = matchingFiles[0];
        element.ymlPath = yamlUri.toString(); // 直接在这里更新
        this.logService.info(`(findPathAndSet) 已更新测试用例 ${element.id} 的 ymlPath 为: ${element.ymlPath}`);
        return yamlUri;
    }
}
