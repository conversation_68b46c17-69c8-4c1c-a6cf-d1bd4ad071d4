{"name": "globalthis", "version": "1.0.1", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ECMAScript spec-compliant polyfill/shim for `globalThis`", "license": "MIT", "main": "index.js", "browser": {"./implementation": "./implementation.browser.js"}, "scripts": {"pretest": "npm run --silent lint && es-shim-api --bound --property", "test": "npm run --silent tests-only", "posttest": "npx aud", "tests-only": "npm run --silent test:implementation && npm run --silent test:shim", "test:native": "node test/native.js", "test:shim": "node test/shimmed.js", "test:implementation": "node test/index.js", "coverage": "covert test/*.js", "coverage:quiet": "covert test/*.js --quiet", "lint": "eslint .", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "prepublish": "npm run --silent build", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/ljharb/System.global.git"}, "keywords": ["window", "self", "global", "globalThis", "System.global", "global object", "global this value", "ECMAScript", "es-shim API", "polyfill", "shim"], "dependencies": {"define-properties": "^1.1.3"}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^15.0.2", "auto-changelog": "^1.16.2", "browserify": "^16.5.0", "covert": "^1.1.1", "eslint": "^6.7.2", "for-each": "^0.3.3", "is": "^3.3.0", "tape": "^4.11.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}}