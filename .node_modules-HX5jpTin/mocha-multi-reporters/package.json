{"name": "mocha-multi-reporters", "version": "1.5.1", "description": "Generate multiple mocha reports in a single mocha execution.", "main": "index.js", "nyc": {"reporter": ["html", "text"]}, "scripts": {"devtest": "nyc mocha --require chai/register-expect --no-coverage --timeout 5000 tests/**/*.test.js*", "lint": "eslint .", "test": "nyc mocha --require chai/register-expect --timeout 5000 tests/**/*.test.js*"}, "author": "<PERSON> <<EMAIL>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>", "<PERSON>", "<PERSON> IV <<EMAIL>>"], "repository": {"type": "git", "url": "https://github.com/stanleyhlng/mocha-multi-reporters"}, "engines": {"node": ">=6.0.0"}, "bugs": "https://github.com/stanleyhlng/mocha-multi-reporters/issues", "homepage": "https://github.com/stanleyhlng/mocha-multi-reporters", "license": "MIT", "publishConfig": {"access": "public"}, "dependencies": {"debug": "^4.1.1", "lodash": "^4.17.15"}, "devDependencies": {"@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "8.3.4", "chai": "4.2.0", "coveralls": "3.1.0", "eslint": "7.11.0", "eslint-config-defaults": "9.0.0", "husky": "4.3.0", "mocha": "8.2.0", "mocha-lcov-reporter": "1.3.0", "nyc": "15.1.0", "sinon": "9.2.0", "standard-version": "8.0.2"}, "peerDependencies": {"mocha": ">=3.1.2"}, "keywords": ["mocha", "reporter"], "eslintConfig": {"extends": "defaults"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}