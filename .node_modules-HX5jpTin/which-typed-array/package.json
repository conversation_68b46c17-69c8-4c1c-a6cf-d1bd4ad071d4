{"name": "which-typed-array", "version": "1.1.11", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "license": "MIT", "main": "index.js", "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run --silent lint", "test": "npm run tests-only && npm run test:harmony", "tests-only": "nyc tape test", "test:harmony": "nyc node --harmony --es-staging test", "posttest": "aud --production", "lint": "eslint --ext=js,mjs .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/inspect-js/which-typed-array.git"}, "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "dependencies": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.0"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "is-callable": "^1.2.7", "make-arrow-function": "^1.2.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.6.5"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}}