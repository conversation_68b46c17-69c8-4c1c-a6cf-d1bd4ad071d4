{"name": "@types/yazl", "version": "2.4.2", "description": "TypeScript definitions for yazl", "license": "MIT", "contributors": [{"name": "taoqf", "url": "https://github.com/taoqf", "githubUsername": "taoqf"}, {"name": "<PERSON>", "url": "https://github.com/seangenabe", "githubUsername": "seangenabe"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/yazl"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "6bd83181fdee560f75eb32b86125c84b55b4954b2672428d5aa82b3a953b0a6b", "typeScriptVersion": "2.1"}