{"name": "@types/gulp-svgmin", "version": "1.2.1", "description": "TypeScript definitions for gulp-svgmin", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Aankhen", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/gulp-svgmin"}, "scripts": {}, "dependencies": {"@types/node": "*", "@types/svgo": "^1", "@types/vinyl": "*"}, "typesPublisherContentHash": "68db828c2d55de4a653f35fc28f8e8cfc6f8078cc94157a11994ba79f02643b3", "typeScriptVersion": "3.5"}