{"name": "@types/wicg-file-system-access", "version": "2020.9.6", "description": "TypeScript definitions for File System Access API", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/wicg-file-system-access", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/RReverser", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=5.0": {"*": ["ts5.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/wicg-file-system-access"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "eb1bd0e3ba1481c46796baf58e8f13cfe69231d882b1e520749a2074583c87c6", "typeScriptVersion": "4.6"}