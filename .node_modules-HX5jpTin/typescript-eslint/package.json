{"name": "typescript-eslint", "version": "8.8.0", "description": "Tooling which enables you to use TypeScript with ESLint", "files": ["dist", "_ts4.3", "README.md", "LICENSE"], "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/typescript-eslint"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io/packages/typescript-eslint", "license": "MIT", "keywords": ["ast", "ecmascript", "javascript", "typescript", "parser", "syntax", "eslint", "eslintplugin", "eslint-plugin"], "scripts": {"build": "tsc -b tsconfig.build.json", "postbuild": "downlevel-dts dist _ts4.3/dist --to=4.3", "clean": "tsc -b tsconfig.build.json --clean", "postclean": "rimraf dist && rimraf _ts4.3 && rimraf coverage", "format": "prettier --write \"./**/*.{ts,mts,cts,tsx,js,mjs,cjs,jsx,json,md,css}\" --ignore-path ../../.prettierignore", "lint": "nx lint", "test": "jest --coverage --passWithNoTests", "typecheck": "tsc --noEmit"}, "dependencies": {"@typescript-eslint/eslint-plugin": "8.8.0", "@typescript-eslint/parser": "8.8.0", "@typescript-eslint/utils": "8.8.0"}, "devDependencies": {"@jest/types": "29.6.3", "downlevel-dts": "*", "jest": "29.7.0", "prettier": "^3.2.5", "rimraf": "*", "typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "typesVersions": {"<4.7": {"*": ["_ts4.3/*"]}}}