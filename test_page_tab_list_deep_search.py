#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 page tab list 深层搜索修复效果
"""

import sys
import os

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def test_page_tab_list_deep_search():
    """测试 page tab list 深层搜索修复"""
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        print(f"测试 page tab list 深层搜索修复效果")
        print("="*60)
        
        # 测试坐标列表
        test_coordinates = [
            (397, 356, "添加按钮"),
            (399, 228, "帮助菜单"),
            (410, 355, "添加按钮中心"),
        ]
        
        results = []
        
        for x, y, description in test_coordinates:
            print(f"\n{len(results)+1}. 测试 {description} - 坐标 ({x}, {y}):")
            print("-" * 50)
            
            # 测试主要的 kdk_getElement_Uni 方法
            result, info = uni.kdk_getElement_Uni(x, y, False, True)
            
            print(f"   结果: {info}")
            if result and not result.get('error'):
                name = result.get('Name', 'Unknown')
                role = result.get('Rolename', 'Unknown')
                coords = result.get('Coords', {})
                print(f"   控件: {name} ({role})")
                print(f"   坐标: {coords}")
                
                # 分析结果
                success = False
                if 'button' in role.lower() and '添加' in name:
                    print(f"   ✅ 正确识别为'添加'按钮")
                    success = True
                elif role.lower() == 'menu' and '帮助' in name:
                    print(f"   ✅ 正确识别为'帮助'菜单")
                    success = True
                elif 'button' in role.lower():
                    print(f"   ✅ 识别为按钮控件: {name}")
                    success = True
                elif role.lower() == 'menu':
                    print(f"   ✅ 识别为菜单控件: {name}")
                    success = True
                elif role.lower() == 'page tab list':
                    print(f"   ❌ 仍然识别为 page tab list")
                    success = False
                elif role.lower() == 'filler':
                    print(f"   ❌ 被错误识别为 filler")
                    success = False
                else:
                    print(f"   ⚠️  识别为其他控件: {name} ({role})")
                    # 检查是否是合理的控件
                    if role.lower() in ['label', 'text', 'menu item']:
                        print(f"   ✅ 识别为合理的控件类型")
                        success = True
                    else:
                        success = False
                
                results.append({
                    'description': description,
                    'coordinates': (x, y),
                    'success': success,
                    'name': name,
                    'role': role
                })
            else:
                print(f"   ❌ 识别失败: {info}")
                results.append({
                    'description': description,
                    'coordinates': (x, y),
                    'success': False,
                    'name': 'N/A',
                    'role': 'N/A'
                })
        
        # 总结结果
        print(f"\n" + "="*60)
        print("修复效果总结:")
        
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        
        for i, result in enumerate(results, 1):
            status = "✅ 成功" if result['success'] else "❌ 失败"
            print(f"{i}. {result['description']} {result['coordinates']}: {status}")
            print(f"   识别为: {result['name']} ({result['role']})")
        
        print(f"\n总体成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if success_count == total_count:
            print(f"\n🎉 page tab list 深层搜索修复完全成功！")
            print(f"所有测试坐标都能正确识别控件")
            return True
        elif success_count > 0:
            print(f"\n🎉 部分修复成功！")
            print(f"大部分坐标能正确识别控件")
            return True
        else:
            print(f"\n❌ 修复效果不明显")
            print(f"需要进一步调试")
            return False
            
    except Exception as e:
        print(f"[ERROR] 测试时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_existing_functionality():
    """测试现有功能是否受到影响"""
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        print(f"\n测试现有功能是否受到影响")
        print("="*60)
        
        # 测试一些基本的控件识别
        basic_tests = [
            (399, 228, "帮助菜单", "menu"),
            (399, 356, "添加按钮", "button"),
        ]
        
        all_good = True
        
        for x, y, description, expected_type in basic_tests:
            print(f"\n测试 {description} ({x}, {y}):")
            
            result, info = uni.kdk_getElement_Uni(x, y, False, True)
            
            if result and not result.get('error'):
                role = result.get('Rolename', 'Unknown').lower()
                name = result.get('Name', 'Unknown')
                
                if expected_type in role:
                    print(f"   ✅ {description} 正常工作: {name} ({role})")
                else:
                    print(f"   ❌ {description} 受到影响: {name} ({role})")
                    all_good = False
            else:
                print(f"   ❌ {description} 识别失败")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"[ERROR] 测试现有功能时发生错误: {e}")
        return False

def main():
    """主函数"""
    print("🔍 测试 page tab list 深层搜索修复效果")
    
    # 测试深层搜索修复
    deep_search_result = test_page_tab_list_deep_search()
    
    # 测试现有功能
    existing_result = test_existing_functionality()
    
    print(f"\n" + "="*60)
    print("最终评估:")
    print(f"深层搜索修复: {'✅ 成功' if deep_search_result else '❌ 失败'}")
    print(f"现有功能保持: {'✅ 正常' if existing_result else '❌ 受影响'}")
    
    if deep_search_result and existing_result:
        print(f"\n🎉 修复完全成功！")
        print(f"page tab list 深层搜索问题已解决，现有功能未受影响")
    elif deep_search_result:
        print(f"\n⚠️ 修复成功但有副作用")
        print(f"需要调整以避免影响现有功能")
    elif existing_result:
        print(f"\n⚠️ 现有功能正常但修复不完整")
        print(f"需要进一步优化深层搜索逻辑")
    else:
        print(f"\n❌ 修复失败且影响现有功能")
        print(f"需要重新评估修复策略")

if __name__ == "__main__":
    main()
