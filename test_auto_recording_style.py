#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完全模拟 auto_recording_manager.py 的调用方式测试控件识别
"""

import sys
import os
import time

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def test_like_auto_recording_manager(x, y):
    """完全模拟 auto_recording_manager.py 的调用方式"""
    print(f"\n{'='*80}")
    print(f"模拟 auto_recording_manager.py 测试坐标 ({x}, {y})")
    print(f"{'='*80}")
    
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例（模拟 auto_recording_manager.py 的初始化）
        uni = UNI()
        
        # 模拟 auto_recording_manager.py 中的缓存清理逻辑
        print("1. 模拟缓存清理...")
        if hasattr(uni, 'desktop_cache') and uni.desktop_cache is not None:
            if isinstance(uni.desktop_cache, dict):
                uni.desktop_cache.clear()
            else:
                uni.desktop_cache = {}
            print("   ✅ 桌面缓存已清除")
        
        if hasattr(uni, 'window_cache'):
            uni.window_cache.clear()
            print("   ✅ 窗口缓存已清除")
        
        if hasattr(uni, 'process_cache'):
            uni.process_cache.clear()
            print("   ✅ 进程缓存已清除")
        
        # 模拟强制桌面刷新
        print("2. 模拟强制桌面刷新...")
        if hasattr(uni, '_last_desktop_refresh'):
            uni._last_desktop_refresh = 0
        
        if hasattr(uni, '_get_fresh_desktop'):
            desktop = uni._get_fresh_desktop()
            app_count = desktop.childCount if desktop else 0
            print(f"   ✅ 桌面刷新完成，应用数: {app_count}")
        
        # 模拟 auto_recording_manager.py 的完整调用
        print("3. 调用 UNI 控件识别（完全模拟 auto_recording_manager.py）...")
        
        # 完全按照 auto_recording_manager.py 的方式调用
        result, info_text = uni.kdk_getElement_Uni(x, y, False, True)
        
        print(f"识别结果: {info_text}")
        
        if result and not result.get('error'):
            print(f"✅ 成功识别控件:")
            print(f"  控件名称: {result.get('Name', 'Unknown')}")
            print(f"  控件角色: {result.get('Rolename', 'Unknown')}")
            print(f"  控件坐标: {result.get('Coords', {})}")
            
            # 检查是否是按钮
            if 'button' in result.get('Rolename', '').lower():
                if '添加' in result.get('Name', ''):
                    print(f"  🎉 完美！正确识别为'添加'按钮")
                    return True
                else:
                    print(f"  ⚠️  是按钮，但不是'添加'按钮: {result.get('Name', '')}")
                    return False
            else:
                print(f"  ❌ 不是按钮控件: {result.get('Rolename', 'Unknown')}")
                return False
        else:
            print(f"❌ 识别失败: {info_text}")
            
            # 如果第一次失败，模拟 auto_recording_manager.py 的重试逻辑
            print("\n4. 模拟重试逻辑...")
            
            # 等待一小段时间
            time.sleep(0.1)
            
            # 第一次重试
            print("   第一次重试...")
            result, info_text = uni.kdk_getElement_Uni(x, y, False, True)
            
            if result and not result.get('error'):
                print(f"   ✅ 重试成功: {result.get('Name', 'Unknown')} ({result.get('Rolename', 'Unknown')})")
                return True
            else:
                print(f"   ❌ 第一次重试失败: {info_text}")
                
                # 等待更长时间
                time.sleep(0.5)
                
                # 最后一次重试
                print("   最后一次重试...")
                result, info_text = uni.kdk_getElement_Uni(x, y, False, True)
                
                if result and not result.get('error'):
                    print(f"   ✅ 最终重试成功: {result.get('Name', 'Unknown')} ({result.get('Rolename', 'Unknown')})")
                    return True
                else:
                    print(f"   ❌ 最终重试失败: {info_text}")
                    return False
            
    except Exception as e:
        print(f"[ERROR] 测试时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_coordinates():
    """测试多个坐标"""
    test_coords = [
        (750, 444),  # 您的测试坐标
        (761, 446),  # 之前成功的坐标
        (740, 447),  # 按钮左侧
        (770, 447),  # 按钮右侧
        (750, 435),  # 按钮上方
        (750, 455),  # 按钮下方
    ]
    
    results = {}
    
    for x, y in test_coords:
        print(f"\n{'='*100}")
        print(f"测试坐标 ({x}, {y})")
        print(f"{'='*100}")
        
        success = test_like_auto_recording_manager(x, y)
        results[(x, y)] = success
        
        # 每次测试之间稍微等待
        time.sleep(0.2)
    
    print(f"\n{'='*100}")
    print("测试结果汇总")
    print(f"{'='*100}")
    
    for (x, y), success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"坐标 ({x}, {y}): {status}")

def main():
    """主函数"""
    # 首先测试您的具体坐标
    success = test_like_auto_recording_manager(750, 444)
    
    if not success:
        print(f"\n{'='*80}")
        print("单个坐标测试失败，进行多坐标对比测试...")
        print(f"{'='*80}")
        test_multiple_coordinates()
    else:
        print(f"\n🎉 测试成功！坐标 (750, 444) 正确识别为'添加'按钮")

if __name__ == "__main__":
    main()
