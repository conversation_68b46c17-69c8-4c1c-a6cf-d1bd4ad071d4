#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试通用的 page tab list 深层搜索修复
"""

import sys
import os

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def test_universal_fix():
    """测试通用的 page tab list 修复"""
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        print(f"测试通用的 page tab list 深层搜索修复")
        print("="*60)
        
        # 测试坐标 (501, 417) - 应该是输入法配置中的"英语"控件
        x, y = 501, 417
        
        print(f"测试坐标: ({x}, {y})")
        print(f"期望找到: '英语'控件或其他交互控件（而不是 page tab list）")
        print("-" * 60)
        
        # 测试主要的 kdk_getElement_Uni 方法
        result, info = uni.kdk_getElement_Uni(x, y, False, True)
        
        print(f"识别结果: {info}")
        if result and not result.get('error'):
            name = result.get('Name', 'Unknown')
            role = result.get('Rolename', 'Unknown')
            coords = result.get('Coords', {})
            print(f"控件: {name} ({role})")
            print(f"坐标: {coords}")
            
            # 分析结果
            if role.lower() == 'page tab list':
                print(f"❌ 仍然识别为 page tab list，通用修复没有生效")
                return False
            elif '英语' in name or 'english' in name.lower():
                print(f"✅ 完美！正确识别为'英语'控件")
                return True
            elif self._is_interactive_control_simple(role.lower()):
                print(f"✅ 成功！识别为交互控件: {name} ({role})")
                return True
            else:
                print(f"⚠️  识别为其他控件: {name} ({role})")
                # 检查是否是合理的控件
                if role.lower() in ['label', 'text', 'menu item', 'list item']:
                    print(f"✅ 可接受的控件类型")
                    return True
                else:
                    print(f"❌ 不理想的控件类型")
                    return False
        else:
            print(f"❌ 识别失败: {info}")
            return False
            
    except Exception as e:
        print(f"[ERROR] 测试时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def _is_interactive_control_simple(role):
    """简单的交互控件判断"""
    interactive_roles = [
        'button', 'push button', 'toggle button', 'check box', 'radio button',
        'text', 'entry', 'combo box', 'list item', 'menu item', 'tab', 'link',
        'spin button', 'slider', 'scroll bar', 'tree item', 'table cell', 'menu'
    ]
    return any(interactive_role in role for interactive_role in interactive_roles)

def test_multiple_coordinates():
    """测试多个坐标"""
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        print(f"\n测试多个坐标的通用修复效果")
        print("="*60)
        
        # 测试坐标列表
        test_coordinates = [
            (501, 417, "输入法配置中的控件"),
            (397, 356, "之前的添加按钮"),
            (399, 228, "帮助菜单"),
        ]
        
        results = []
        
        for x, y, description in test_coordinates:
            print(f"\n测试 {description} - 坐标 ({x}, {y}):")
            
            result, info = uni.kdk_getElement_Uni(x, y, False, True)
            
            if result and not result.get('error'):
                name = result.get('Name', 'Unknown')
                role = result.get('Rolename', 'Unknown')
                print(f"  结果: {name} ({role})")
                
                if role.lower() == 'page tab list':
                    print(f"  ❌ 仍然是 page tab list")
                    results.append(False)
                elif _is_interactive_control_simple(role.lower()):
                    print(f"  ✅ 成功识别为交互控件")
                    results.append(True)
                else:
                    print(f"  ⚠️  其他控件类型")
                    results.append(True)  # 只要不是 page tab list 就算成功
            else:
                print(f"  ❌ 识别失败")
                results.append(False)
        
        success_count = sum(results)
        total_count = len(results)
        
        print(f"\n总体结果: {success_count}/{total_count} 成功")
        
        return success_count == total_count
        
    except Exception as e:
        print(f"[ERROR] 测试多个坐标时发生错误: {e}")
        return False

def main():
    """主函数"""
    print("🔍 测试通用的 page tab list 深层搜索修复")
    
    # 测试单个坐标
    single_result = test_universal_fix()
    
    # 测试多个坐标
    multiple_result = test_multiple_coordinates()
    
    print(f"\n" + "="*60)
    print("最终评估:")
    print(f"单个坐标测试: {'✅ 成功' if single_result else '❌ 失败'}")
    print(f"多个坐标测试: {'✅ 成功' if multiple_result else '❌ 失败'}")
    
    if single_result and multiple_result:
        print(f"\n🎉 通用 page tab list 深层搜索修复完全成功！")
        print(f"现在所有应用程序中的 page tab list 都能正确深入搜索了")
    elif single_result or multiple_result:
        print(f"\n🎉 部分修复成功！")
        print(f"大部分情况下能正确处理 page tab list")
    else:
        print(f"\n❌ 通用修复效果不明显")
        print(f"需要进一步优化深层搜索逻辑")

if __name__ == "__main__":
    main()
