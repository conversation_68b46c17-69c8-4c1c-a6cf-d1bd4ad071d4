#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找"添加"按钮的实际位置
"""

import sys
import os
import time
import traceback

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def find_add_button():
    """查找"添加"按钮"""
    print(f"\n{'='*80}")
    print("查找'添加'按钮的位置")
    print(f"{'='*80}")
    
    try:
        import pyatspi
        
        # 创建UNI实例
        uni = UNI()
        os.environ['DISPLAY'] = ':0'
        
        # 获取桌面对象
        desktop = uni._get_fresh_desktop()
        if not desktop:
            print("无法获取桌面对象")
            return
            
        print(f"桌面应用程序数量: {desktop.childCount}")
        
        # 查找打印机应用程序
        printer_app = None
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                if app and app.name == 'system-config-printer':
                    printer_app = app
                    print(f"找到打印机应用程序: {app.name}")
                    break
            except:
                continue
                
        if not printer_app:
            print("未找到打印机应用程序")
            return
            
        # 递归查找所有控件，寻找包含"添加"的控件
        def search_widgets(widget, depth=0, max_depth=10):
            if depth > max_depth:
                return []
                
            found_widgets = []
            
            try:
                # 获取控件信息
                role = widget.getRoleName()
                name = widget.name if widget.name else ''
                
                # 检查是否包含"添加"关键词
                if ('添加' in name.lower() or 'add' in name.lower() or 
                    '新增' in name.lower() or 'new' in name.lower() or
                    role.lower() in ['push button', 'button']):
                    
                    # 获取控件位置
                    try:
                        component = widget.queryComponent()
                        if component:
                            extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                            
                            found_widgets.append({
                                'widget': widget,
                                'name': name,
                                'role': role,
                                'position': (extents.x, extents.y),
                                'size': (extents.width, extents.height),
                                'depth': depth
                            })
                            
                            indent = "  " * depth
                            print(f"{indent}🎯 找到候选控件: '{name}' ({role}) - "
                                  f"位置:({extents.x},{extents.y}) 大小:({extents.width}x{extents.height})")
                    except:
                        pass
                
                # 递归搜索子控件
                for i in range(widget.childCount):
                    try:
                        child = widget.getChildAtIndex(i)
                        if child:
                            child_results = search_widgets(child, depth + 1, max_depth)
                            found_widgets.extend(child_results)
                    except:
                        continue
                        
            except Exception as e:
                print(f"{'  ' * depth}❌ 搜索控件时出错: {e}")
                
            return found_widgets
        
        print(f"\n搜索包含'添加'或按钮类型的控件:")
        found_widgets = search_widgets(printer_app)
        
        if found_widgets:
            print(f"\n找到 {len(found_widgets)} 个候选控件:")
            for i, widget_info in enumerate(found_widgets):
                print(f"{i+1}. '{widget_info['name']}' ({widget_info['role']})")
                print(f"   位置: {widget_info['position']}")
                print(f"   大小: {widget_info['size']}")
                print(f"   深度: {widget_info['depth']}")
                
                # 测试这个控件在坐标 (761, 446) 是否可见
                x, y = widget_info['position']
                w, h = widget_info['size']
                if (x <= 761 <= x + w and y <= 446 <= y + h):
                    print(f"   ✅ 包含坐标 (761, 446)")
                else:
                    print(f"   ❌ 不包含坐标 (761, 446)")
                print()
        else:
            print("未找到包含'添加'关键词的控件")
            
        # 额外搜索：查找所有按钮类型的控件
        print(f"\n{'='*60}")
        print("搜索所有按钮类型的控件:")
        
        def search_buttons(widget, depth=0, max_depth=8):
            if depth > max_depth:
                return []
                
            found_buttons = []
            
            try:
                role = widget.getRoleName().lower()
                name = widget.name if widget.name else 'unnamed'
                
                if 'button' in role:
                    try:
                        component = widget.queryComponent()
                        if component:
                            extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                            
                            found_buttons.append({
                                'name': name,
                                'role': role,
                                'position': (extents.x, extents.y),
                                'size': (extents.width, extents.height),
                                'depth': depth
                            })
                    except:
                        pass
                
                # 递归搜索子控件
                for i in range(widget.childCount):
                    try:
                        child = widget.getChildAtIndex(i)
                        if child:
                            child_buttons = search_buttons(child, depth + 1, max_depth)
                            found_buttons.extend(child_buttons)
                    except:
                        continue
                        
            except:
                pass
                
            return found_buttons
        
        buttons = search_buttons(printer_app)
        if buttons:
            print(f"找到 {len(buttons)} 个按钮控件:")
            for i, button in enumerate(buttons):
                print(f"{i+1}. '{button['name']}' ({button['role']})")
                print(f"   位置: {button['position']} 大小: {button['size']}")
                
                # 检查是否包含目标坐标
                x, y = button['position']
                w, h = button['size']
                if (x <= 761 <= x + w and y <= 446 <= y + h):
                    print(f"   ✅ 包含坐标 (761, 446)")
                print()
        else:
            print("未找到按钮控件")
            
    except Exception as e:
        print(f"[ERROR] 查找添加按钮时发生错误: {e}")
        traceback.print_exc()

def main():
    """主函数"""
    find_add_button()

if __name__ == "__main__":
    main()
