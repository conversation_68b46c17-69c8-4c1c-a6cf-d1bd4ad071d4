#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试智能深度搜索方法
"""

import sys
import os
import time
import traceback

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def test_smart_search(x, y):
    """测试智能深度搜索"""
    print(f"\n{'='*80}")
    print(f"测试智能深度搜索，坐标: ({x}, {y})")
    print(f"{'='*80}")
    
    try:
        # 创建UNI实例
        uni = UNI()
        os.environ['DISPLAY'] = ':0'
        
        # 获取活动窗口
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = uni._get_active_window2(x, y)
        
        if not active_window:
            print("未找到活动窗口")
            return
            
        print(f"活动窗口: {active_window.name}")
        print(f"窗口区域: {activewindow_region}")
        
        # 直接调用智能深度搜索方法
        print(f"\n调用智能深度搜索方法...")
        result = uni._smart_deep_search_at_point(active_window, x, y, activewindow_region)
        
        if result:
            print(f"\n智能深度搜索结果:")
            print(f"控件名称: {result.name if result.name else 'unnamed'}")
            print(f"控件角色: {result.getRoleName()}")
            
            # 获取控件位置
            try:
                import pyatspi
                component = result.queryComponent()
                if component:
                    extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                    print(f"控件位置: ({extents.x}, {extents.y})")
                    print(f"控件大小: ({extents.width}, {extents.height})")
            except:
                pass
        else:
            print("智能深度搜索未找到控件")
            
    except Exception as e:
        print(f"[ERROR] 测试智能深度搜索时发生错误: {e}")
        traceback.print_exc()

def main():
    """主函数"""
    test_smart_search(761, 446)

if __name__ == "__main__":
    main()
