#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试最终修复效果
"""

import sys
import os
import time
import traceback

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def test_final_fix(x, y):
    """测试最终修复效果"""
    print(f"\n{'='*80}")
    print(f"测试坐标 ({x}, {y}) 的控件识别修复效果")
    print(f"{'='*80}")
    
    try:
        # 创建UNI实例
        uni = UNI()
        os.environ['DISPLAY'] = ':0'
        
        # 使用kdk_getElement_Uni方法测试
        print(f"使用kdk_getElement_Uni方法测试...")
        widget_info, info_text = uni.kdk_getElement_Uni(x, y, False)
        
        print(f"识别结果: {info_text}")
        
        if widget_info and not widget_info.get('error'):
            print(f"\n✅ 成功识别控件:")
            print(f"  控件名称: {widget_info.get('Name', 'Unknown')}")
            print(f"  控件角色: {widget_info.get('Rolename', 'Unknown')}")
            print(f"  控件坐标: {widget_info.get('Coords', {})}")
            
            # 检查是否是按钮
            if 'button' in widget_info.get('Rolename', '').lower():
                print(f"  🎯 这是一个按钮控件！修复成功！")
            else:
                print(f"  ❌ 这不是按钮控件，可能还有问题")
        else:
            print(f"❌ 识别失败")
            
    except Exception as e:
        print(f"[ERROR] 测试时发生错误: {e}")
        traceback.print_exc()

def main():
    """主函数"""
    # 测试问题坐标
    test_final_fix(761, 446)
    
    # 测试一些其他坐标进行对比
    print(f"\n\n{'='*80}")
    print("对比测试其他坐标")
    print(f"{'='*80}")
    
    test_coordinates = [
        (750, 446),  # 左侧
        (770, 446),  # 右侧
        (761, 436),  # 上方
        (761, 456),  # 下方
    ]
    
    for x, y in test_coordinates:
        print(f"\n--- 测试坐标 ({x}, {y}) ---")
        try:
            uni = UNI()
            os.environ['DISPLAY'] = ':0'
            
            widget_info, info_text = uni.kdk_getElement_Uni(x, y, False)
            if widget_info and not widget_info.get('error'):
                name = widget_info.get('Name', 'Unknown')
                role = widget_info.get('Rolename', 'Unknown')
                print(f"控件: {name} ({role})")
                
                if 'button' in role.lower():
                    print(f"  🎯 这是按钮控件")
            else:
                print(f"识别失败: {info_text}")
        except Exception as e:
            print(f"错误: {e}")

if __name__ == "__main__":
    main()
