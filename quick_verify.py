#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速验证修复效果
"""

import sys
import os

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

def quick_verify():
    """快速验证"""
    try:
        from UNI import UNI
        
        os.environ['DISPLAY'] = ':0'
        uni = UNI()
        
        # 测试您的坐标
        x, y = 750, 444
        result, info_text = uni.kdk_getElement_Uni(x, y, False, True)
        
        print(f"坐标 ({x}, {y}) 识别结果:")
        
        if result and not result.get('error'):
            name = result.get('Name', 'Unknown')
            role = result.get('Rolename', 'Unknown')
            print(f"  控件: {name} ({role})")
            
            if 'button' in role.lower() and '添加' in name:
                print("  ✅ 修复成功！")
                return True
            else:
                print(f"  ❌ 修复失败！期望'添加'按钮，实际: {name} ({role})")
                return False
        else:
            print(f"  ❌ 识别失败: {info_text}")
            return False
            
    except Exception as e:
        print(f"❌ 验证出错: {e}")
        return False

if __name__ == "__main__":
    success = quick_verify()
    exit(0 if success else 1)
