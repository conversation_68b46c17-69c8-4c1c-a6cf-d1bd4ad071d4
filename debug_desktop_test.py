#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
实时调试桌面环境中的控件识别问题
"""

import sys
import os
import time

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def debug_current_desktop_state(x, y):
    """调试当前桌面状态下的控件识别"""
    print(f"\n{'='*80}")
    print(f"实时调试桌面环境 - 坐标 ({x}, {y})")
    print(f"{'='*80}")

    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'

        # 创建UNI实例
        uni = UNI()

        # 1. 首先检查当前活动窗口
        print("1. 检查当前活动窗口...")
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = uni._get_active_window2(x, y)

        if active_window:
            print(f"   活动窗口: {active_window.name}")
            print(f"   进程ID: {processid}")
            print(f"   窗口区域: {activewindow_region}")
            print(f"   窗口角色: {windowRoleName}")
            print(f"   子控件数量: {windowChildCount}")
        else:
            print("   ❌ 未找到活动窗口")
            return

        # 2. 检查"添加"按钮是否仍然存在且位置正确
        print("\n2. 搜索当前所有按钮控件...")
        try:
            import pyatspi

            # 获取桌面对象
            desktop = uni._get_fresh_desktop()

            # 查找打印机应用程序
            printer_app = None
            for i in range(desktop.childCount):
                try:
                    app = desktop.getChildAtIndex(i)
                    if app and app.name == 'system-config-printer':
                        printer_app = app
                        break
                except:
                    continue

            if printer_app:
                print(f"   ✅ 找到打印机应用程序")

                # 查找所有按钮
                def find_all_buttons(widget, depth=0, max_depth=10):
                    if depth > max_depth:
                        return []

                    buttons = []
                    try:
                        role = widget.getRoleName().lower()
                        name = widget.name if widget.name else 'unnamed'

                        if 'button' in role:
                            try:
                                component = widget.queryComponent()
                                if component:
                                    extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                                    buttons.append({
                                        'name': name,
                                        'role': role,
                                        'position': (extents.x, extents.y),
                                        'size': (extents.width, extents.height),
                                        'depth': depth
                                    })
                            except:
                                pass

                        # 递归搜索子控件
                        for i in range(widget.childCount):
                            try:
                                child = widget.getChildAtIndex(i)
                                if child:
                                    child_buttons = find_all_buttons(child, depth + 1, max_depth)
                                    buttons.extend(child_buttons)
                            except:
                                continue
                    except:
                        pass

                    return buttons

                buttons = find_all_buttons(printer_app)

                if buttons:
                    print(f"   找到 {len(buttons)} 个按钮控件:")
                    for i, button in enumerate(buttons):
                        x1, y1 = button['position']
                        w, h = button['size']
                        contains_point = (x1 <= x <= x1 + w and y1 <= y <= y1 + h)
                        marker = "🎯" if contains_point else "  "

                        print(f"   {marker} {i+1}. '{button['name']}' ({button['role']})")
                        print(f"      位置: ({x1}, {y1}) 大小: ({w}, {h})")
                        print(f"      包含坐标({x}, {y}): {contains_point}")

                        if '添加' in button['name']:
                            print(f"      ⭐ 这是'添加'按钮！")
                else:
                    print(f"   ❌ 未找到任何按钮控件")
            else:
                print(f"   ❌ 未找到打印机应用程序")

        except Exception as e:
            print(f"   ❌ 搜索按钮时出错: {e}")

        # 3. 执行完整的控件识别流程并显示详细过程
        print(f"\n3. 执行完整的控件识别流程...")

        # 清除缓存
        if hasattr(uni, 'desktop_cache'):
            uni.desktop_cache = {}
        if hasattr(uni, '_last_desktop_refresh'):
            uni._last_desktop_refresh = 0

        # 调用识别
        result, info_text = uni.kdk_getElement_Uni(x, y, False, True)

        print(f"\n识别结果: {info_text}")

        if result and not result.get('error'):
            print(f"✅ 识别到控件:")
            print(f"  控件名称: {result.get('Name', 'Unknown')}")
            print(f"  控件角色: {result.get('Rolename', 'Unknown')}")
            print(f"  控件坐标: {result.get('Coords', {})}")

            if result.get('Rolename', '').lower() == 'page tab list':
                print(f"  ❌ 仍然识别为 page tab list！")
                print(f"  🔍 这说明修复可能没有生效，或者按钮不在当前坐标")
            elif 'button' in result.get('Rolename', '').lower():
                print(f"  ✅ 正确识别为按钮控件！")
            else:
                print(f"  ⚠️  识别为其他控件类型")
        else:
            print(f"❌ 识别失败")

        # 4. 测试智能深度搜索的详细过程
        print(f"\n4. 测试智能深度搜索的详细过程...")

        if active_window:
            result_element = uni._smart_deep_search_at_point(active_window, x, y, activewindow_region)

            if result_element:
                print(f"智能深度搜索结果:")
                print(f"  控件名称: {result_element.name if result_element.name else 'unnamed'}")
                print(f"  控件角色: {result_element.getRoleName()}")

                if result_element.getRoleName().lower() == 'page tab list':
                    print(f"  ❌ 智能深度搜索也返回 page tab list")
                    print(f"  🔍 这说明按钮控件没有被正确找到或评分")
                elif 'button' in result_element.getRoleName().lower():
                    print(f"  ✅ 智能深度搜索正确找到按钮")
            else:
                print(f"智能深度搜索未找到控件")

    except Exception as e:
        print(f"[ERROR] 调试时发生错误: {e}")
        import traceback
        traceback.print_exc()

def test_multiple_coordinates_around_button():
    """测试按钮周围的多个坐标"""
    print(f"\n{'='*80}")
    print("测试按钮周围的多个坐标")
    print(f"{'='*80}")

    # 基于之前测试成功的按钮位置 (727, 431, 58, 32)
    button_x, button_y, button_w, button_h = 727, 431, 58, 32

    test_coords = [
        # 按钮中心
        (button_x + button_w//2, button_y + button_h//2),
        # 按钮四角
        (button_x + 5, button_y + 5),  # 左上角
        (button_x + button_w - 5, button_y + 5),  # 右上角
        (button_x + 5, button_y + button_h - 5),  # 左下角
        (button_x + button_w - 5, button_y + button_h - 5),  # 右下角
        # 您的新测试坐标
        (458, 330),
        # 之前的测试坐标
        (750, 444),
    ]

    for i, (x, y) in enumerate(test_coords):
        print(f"\n--- 测试坐标 {i+1}: ({x}, {y}) ---")

        try:
            uni = UNI()
            os.environ['DISPLAY'] = ':0'

            result, info_text = uni.kdk_getElement_Uni(x, y, False, True)

            if result and not result.get('error'):
                name = result.get('Name', 'Unknown')
                role = result.get('Rolename', 'Unknown')
                print(f"识别结果: {name} ({role})")

                if 'button' in role.lower():
                    print(f"✅ 正确识别为按钮")
                elif role.lower() == 'page tab list':
                    print(f"❌ 识别为 page tab list")
                else:
                    print(f"⚠️  识别为其他: {role}")
            else:
                print(f"❌ 识别失败: {info_text}")

        except Exception as e:
            print(f"❌ 测试出错: {e}")

def main():
    """主函数"""
    # 调试您的新坐标
    debug_current_desktop_state(458, 330)

    # 测试按钮周围的多个坐标
    test_multiple_coordinates_around_button()

if __name__ == "__main__":
    main()
