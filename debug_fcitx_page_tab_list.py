#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试输入法配置中的 page tab list 深层搜索
"""

import sys
import os

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def debug_fcitx_page_tab_list():
    """调试输入法配置中的 page tab list"""
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        # 英语控件坐标
        x, y = 501, 417
        
        print(f"调试输入法配置中的 page tab list 深层搜索")
        print(f"坐标: ({x}, {y})")
        print("="*60)
        
        # 获取桌面对象
        desktop = uni._get_fresh_desktop()
        
        # 查找输入法配置应用程序
        fcitx_app = None
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                if app and app.name == 'fcitx-config-gtk3':
                    fcitx_app = app
                    print(f"找到输入法配置应用程序: {app.name}")
                    break
            except:
                continue
        
        if not fcitx_app:
            print(f"❌ 未找到输入法配置应用程序")
            return False
        
        # 1. 测试 _find_element_at_coordinates 方法
        print(f"\n1. 测试 _find_element_at_coordinates 方法:")
        print("-" * 50)
        
        element = uni._find_element_at_coordinates(fcitx_app, x, y)
        
        print("-" * 50)
        if element:
            name = element.name if element.name else 'unnamed'
            role = element.getRoleName()
            print(f"结果: {name} ({role})")
            
            # 获取控件位置和子控件信息
            try:
                import pyatspi
                component = element.queryComponent()
                if component:
                    extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                    print(f"位置: ({extents.x}, {extents.y}) 大小: {extents.width}x{extents.height}")
                
                print(f"子控件数: {element.childCount}")
                
                # 如果是 page tab list，显示其子控件
                if role.lower() == 'page tab list':
                    print(f"page tab list 的子控件:")
                    for i in range(min(element.childCount, 5)):  # 只显示前5个
                        try:
                            child = element.getChildAtIndex(i)
                            if child:
                                child_name = child.name if child.name else 'unnamed'
                                child_role = child.getRoleName()
                                child_component = child.queryComponent()
                                if child_component:
                                    child_extents = child_component.getExtents(pyatspi.DESKTOP_COORDS)
                                    print(f"  [{i}] {child_name} ({child_role}) - 位置:({child_extents.x}, {child_extents.y}) 大小:{child_extents.width}x{child_extents.height}")
                                    
                                    # 检查是否包含目标坐标
                                    contains = (child_extents.x <= x < child_extents.x + child_extents.width and
                                              child_extents.y <= y < child_extents.y + child_extents.height)
                                    print(f"      包含坐标({x}, {y}): {contains}")
                                    
                                    # 如果包含坐标，继续查看其子控件
                                    if contains and child.childCount > 0:
                                        print(f"      子控件数: {child.childCount}")
                                        for j in range(min(child.childCount, 3)):  # 只显示前3个
                                            try:
                                                grandchild = child.getChildAtIndex(j)
                                                if grandchild:
                                                    gc_name = grandchild.name if grandchild.name else 'unnamed'
                                                    gc_role = grandchild.getRoleName()
                                                    print(f"        [{j}] {gc_name} ({gc_role})")
                                                    
                                                    # 检查是否是"英语"控件
                                                    if '英语' in gc_name or 'english' in gc_name.lower():
                                                        print(f"        🎯 找到'英语'控件!")
                                            except:
                                                continue
                        except:
                            continue
                
            except Exception as e:
                print(f"获取控件信息失败: {e}")
            
            if role.lower() == 'page tab list':
                print(f"❌ 仍然返回 page tab list，深层搜索没有生效")
                return False
            elif '英语' in name or 'english' in name.lower():
                print(f"✅ 正确识别为'英语'控件")
                return True
            else:
                print(f"⚠️  识别为其他控件")
                return False
        else:
            print(f"❌ 未找到控件")
            return False
        
    except Exception as e:
        print(f"[ERROR] 调试时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 调试输入法配置中的 page tab list 深层搜索")
    
    success = debug_fcitx_page_tab_list()
    
    if not success:
        print(f"\n💡 问题确认:")
        print(f"page tab list 深层搜索修复在输入法配置应用程序中没有生效")
        print(f"需要进一步增强深层搜索逻辑")

if __name__ == "__main__":
    main()
