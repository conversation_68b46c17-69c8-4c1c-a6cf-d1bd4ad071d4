#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专门测试 _find_element_at_coordinates 方法
"""

import sys
import os

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def test_find_element_at_coordinates():
    """测试 _find_element_at_coordinates 方法"""
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        # 测试坐标
        x, y = 458, 330
        
        print(f"测试 _find_element_at_coordinates 方法")
        print(f"坐标: ({x}, {y})")
        print("="*60)
        
        # 获取桌面对象
        desktop = uni._get_fresh_desktop()
        
        # 查找打印机应用程序
        printer_app = None
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                if app and app.name == 'system-config-printer':
                    printer_app = app
                    print(f"找到打印机应用程序: {app.name}")
                    break
            except:
                continue
        
        if not printer_app:
            print("❌ 未找到打印机应用程序")
            return False
        
        # 直接调用 _find_element_at_coordinates 方法
        print(f"\n调用 _find_element_at_coordinates({x}, {y})...")
        print("-" * 60)
        
        element = uni._find_element_at_coordinates(printer_app, x, y)
        
        print("-" * 60)
        print(f"_find_element_at_coordinates 结果:")
        
        if element:
            name = element.name if element.name else 'unnamed'
            role = element.getRoleName()
            print(f"  控件名称: {name}")
            print(f"  控件角色: {role}")
            
            # 获取控件位置
            try:
                import pyatspi
                component = element.queryComponent()
                if component:
                    extents = component.getExtents(pyatspi.DESKTOP_COORDS)
                    print(f"  控件位置: ({extents.x}, {extents.y})")
                    print(f"  控件大小: ({extents.width}, {extents.height})")
            except:
                pass
            
            # 分析结果
            if 'button' in role.lower() and '添加' in name:
                print(f"  ✅ 正确识别为'添加'按钮！")
                return True
            elif role.lower() == 'page tab list':
                print(f"  ❌ 仍然识别为 page tab list")
                return False
            else:
                print(f"  ⚠️  识别为其他控件: {name} ({role})")
                return False
        else:
            print(f"  ❌ 未找到控件")
            return False
            
    except Exception as e:
        print(f"[ERROR] 测试时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_warning_dialog_detection():
    """测试警告窗口专项检测"""
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        # 测试坐标
        x, y = 458, 330
        
        print(f"\n\n测试警告窗口专项检测")
        print(f"坐标: ({x}, {y})")
        print("="*60)
        
        # 直接调用警告窗口检测方法
        warning_element = uni._find_warning_dialog_at_point(x, y)
        
        print(f"警告窗口检测结果:")
        
        if warning_element:
            name = warning_element.name if warning_element.name else 'unnamed'
            role = warning_element.getRoleName()
            print(f"  控件名称: {name}")
            print(f"  控件角色: {role}")
            
            if 'button' in role.lower() and '添加' in name:
                print(f"  ✅ 警告窗口检测正确识别为'添加'按钮！")
                return True
            elif role.lower() == 'page tab list':
                print(f"  ❌ 警告窗口检测识别为 page tab list")
                return False
            else:
                print(f"  ⚠️  警告窗口检测识别为其他: {name} ({role})")
                return False
        else:
            print(f"  ℹ️  警告窗口检测未找到控件")
            return None
            
    except Exception as e:
        print(f"[ERROR] 测试警告窗口检测时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 测试控件识别的具体方法")
    
    # 测试 _find_element_at_coordinates 方法
    result1 = test_find_element_at_coordinates()
    
    # 测试警告窗口专项检测
    result2 = test_warning_dialog_detection()
    
    print(f"\n" + "="*60)
    print("测试结果总结:")
    print(f"_find_element_at_coordinates: {'✅ 成功' if result1 else '❌ 失败'}")
    print(f"警告窗口专项检测: {'✅ 成功' if result2 else '❌ 失败' if result2 is not None else 'ℹ️ 未找到'}")
    
    if result1:
        print(f"\n🎉 _find_element_at_coordinates 修复成功！")
    elif result2:
        print(f"\n🎉 警告窗口专项检测工作正常！")
    else:
        print(f"\n❌ 两个方法都有问题，需要进一步调试")

if __name__ == "__main__":
    main()
