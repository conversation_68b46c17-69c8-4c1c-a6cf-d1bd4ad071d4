#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证修复效果的简单脚本
"""

import sys
import os

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

def verify_fix():
    """验证修复效果"""
    try:
        from UNI import UNI
        
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        # 测试坐标
        x, y = 750, 444
        
        print(f"测试坐标: ({x}, {y})")
        print("="*50)
        
        # 调用控件识别
        widget_info, info_text = uni.kdk_getElement_Uni(x, y, False)
        
        print(f"识别结果: {info_text}")
        
        if widget_info and not widget_info.get('error'):
            name = widget_info.get('Name', 'Unknown')
            role = widget_info.get('Rolename', 'Unknown')
            coords = widget_info.get('Coords', {})
            
            print(f"控件名称: {name}")
            print(f"控件角色: {role}")
            print(f"控件坐标: {coords}")
            
            if 'button' in role.lower() and '添加' in name:
                print("🎉 修复成功！正确识别为'添加'按钮")
                return True
            else:
                print(f"❌ 修复失败！识别为: {name} ({role})")
                return False
        else:
            print(f"❌ 识别失败: {info_text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

if __name__ == "__main__":
    success = verify_fix()
    if success:
        print("\n✅ 验证通过：修复成功！")
    else:
        print("\n❌ 验证失败：修复可能有问题")
