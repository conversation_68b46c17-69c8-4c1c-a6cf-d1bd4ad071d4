#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 page tab list 超低优先级修复效果
"""

import sys
import os

# 添加scripts目录到路径
sys.path.insert(0, 'scripts')

try:
    from UNI import UNI
    print("[INFO] UNI模块导入成功", file=sys.stderr)
except ImportError as e:
    print(f"[ERROR] UNI模块导入失败: {e}", file=sys.stderr)
    sys.exit(1)

def test_page_tab_list_fix():
    """测试 page tab list 超低优先级修复"""
    try:
        # 设置环境
        os.environ['DISPLAY'] = ':0'
        
        # 创建UNI实例
        uni = UNI()
        
        # 测试坐标
        x, y = 458, 330
        
        print(f"测试 page tab list 超低优先级修复")
        print(f"坐标: ({x}, {y})")
        print("="*60)
        
        # 1. 测试主要的控件识别方法
        print(f"1. 测试 kdk_getElement_Uni 方法:")
        result, info = uni.kdk_getElement_Uni(x, y, False, True)
        
        print(f"   结果: {info}")
        if result and not result.get('error'):
            name = result.get('Name', 'Unknown')
            role = result.get('Rolename', 'Unknown')
            print(f"   控件: {name} ({role})")
            
            if 'button' in role.lower() and '添加' in name:
                print(f"   ✅ 正确识别为'添加'按钮！")
                success1 = True
            elif role.lower() == 'page tab list':
                print(f"   ❌ 仍然识别为 page tab list")
                success1 = False
            else:
                print(f"   ⚠️  识别为其他: {name} ({role})")
                success1 = False
        else:
            print(f"   ❌ 识别失败")
            success1 = False
        
        # 2. 测试 _find_element_at_coordinates 方法
        print(f"\n2. 测试 _find_element_at_coordinates 方法:")
        
        # 获取桌面对象
        desktop = uni._get_fresh_desktop()
        
        # 查找打印机应用程序
        printer_app = None
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                if app and app.name == 'system-config-printer':
                    printer_app = app
                    break
            except:
                continue
        
        if printer_app:
            print(f"   找到打印机应用程序")
            
            # 直接调用 _find_element_at_coordinates
            element = uni._find_element_at_coordinates(printer_app, x, y)
            
            if element:
                name = element.name if element.name else 'unnamed'
                role = element.getRoleName()
                print(f"   识别结果: {name} ({role})")
                
                if 'button' in role.lower() and '添加' in name:
                    print(f"   ✅ _find_element_at_coordinates 正确识别为'添加'按钮！")
                    success2 = True
                elif role.lower() == 'page tab list':
                    print(f"   ❌ _find_element_at_coordinates 仍然识别为 page tab list")
                    success2 = False
                else:
                    print(f"   ⚠️  _find_element_at_coordinates 识别为其他: {name} ({role})")
                    success2 = False
            else:
                print(f"   ❌ _find_element_at_coordinates 未找到控件")
                success2 = False
        else:
            print(f"   ❌ 未找到打印机应用程序")
            success2 = False
        
        # 3. 测试评分算法
        print(f"\n3. 测试评分算法:")
        
        # 模拟创建一个 page tab list 控件和按钮控件，比较得分
        class MockElement:
            def __init__(self, role, name='test', child_count=0):
                self.role = role
                self.name = name
                self.child_count = child_count
            
            def getRoleName(self):
                return self.role
            
            @property
            def childCount(self):
                return self.child_count
        
        # 创建模拟控件
        page_tab_list_element = MockElement('page tab list', 'unnamed', 3)
        button_element = MockElement('push button', '添加', 0)
        
        # 计算得分
        page_tab_list_score = uni._calculate_control_priority_score(page_tab_list_element, 60960, 2)
        button_score = uni._calculate_control_priority_score(button_element, 1856, 6)
        
        print(f"   page tab list 得分: {page_tab_list_score}")
        print(f"   push button 得分: {button_score}")
        
        if button_score > page_tab_list_score:
            print(f"   ✅ 按钮得分高于 page tab list，评分算法修复成功！")
            success3 = True
        else:
            print(f"   ❌ page tab list 得分仍然高于按钮，评分算法需要进一步调整")
            success3 = False
        
        # 总结
        print(f"\n" + "="*60)
        print("修复效果总结:")
        print(f"kdk_getElement_Uni: {'✅ 成功' if success1 else '❌ 失败'}")
        print(f"_find_element_at_coordinates: {'✅ 成功' if success2 else '❌ 失败'}")
        print(f"评分算法: {'✅ 成功' if success3 else '❌ 失败'}")
        
        if success1 and success2 and success3:
            print(f"\n🎉 page tab list 超低优先级修复完全成功！")
            print(f"auto_recording_manager.py 现在应该能正确识别按钮控件了！")
            return True
        elif success1:
            print(f"\n🎉 主要方法修复成功！")
            print(f"auto_recording_manager.py 应该能正确识别按钮控件了！")
            return True
        else:
            print(f"\n❌ 修复可能不完整，需要进一步调试")
            return False
            
    except Exception as e:
        print(f"[ERROR] 测试时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_page_tab_list_fix()
    
    if success:
        print(f"\n🚀 请重新测试您的 auto_recording_manager.py！")
        print(f"坐标 (458, 330) 现在应该正确识别为'添加'按钮")
    else:
        print(f"\n💡 建议重启 auto_recording_manager.py 程序以加载新的修复")

if __name__ == "__main__":
    main()
